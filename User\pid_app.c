#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include "pid_app.h"
#include "pid.h"
#include "motor_app.h"
#include "qei_app.h"

// PID控制器实例
PID_T pid_left;  // 左轮PID控制器
PID_T pid_right; // 右轮PID控制器

// 默认PID参数
PidParams_t pid_params_left = {
    .kp = 0.5f,        // 比例Kp，响应速度
    .ki = 0.000f,      // 积分Ki，减小静态误差
    .kd = 0.00f,      // 微分Kd，增加稳定性
    .sample_time = 0.02f,
    .out_min = -100.0f,
    .out_max = 100.0f,
    .i_min = -80.0f,
    .i_max = 80.0f,
    .deadzone = 1      // 最小误差，小于此认为已达到目标
};

PidParams_t pid_params_right = {
    .kp = 1.0f,     // 比例系数
    .ki = 0.0100f,      // 积分系数
    .kd = 0.00f,     // 微分系数
    .sample_time = 0.05f,
    .out_min = -99.0f,
    .out_max = 99.0f,
    .i_min = -80.0f,
    .i_max = 80.0f,
    .deadzone = 0    
};

// PID目标速度
float target_left_rpm = 0.0f;
float target_right_rpm = 0.0f;

// 当前实际速度
float current_left_rpm = 0.0f;
float current_right_rpm = 0.0f;

// PID控制状态标志
static bool pid_running = false;

// 电机输出值
uint8_t motor_left_speed, motor_right_speed;
motor_direction_t motor_left_dir, motor_right_dir;

// 调试标志，显示调试信息
#define DEBUG_PID 0

/**
 * @brief 限幅函数
 * @param value 输入值
 * @param min 最小值
 * @param max 最大值
 * @return 限幅后的值
 */
static float constrain(float value, float min, float max)
{
    if (value < min)
        return min;
    else if (value > max)
        return max;
    else
        return value;
}

/**
 * @brief 积分限幅函数
 * @param pid PID控制器
 * @param min 最小值
 * @param max 最大值
 * @note 在使用增量式PID控制时此函数不需要，但为了可能切换回位置式PID而保留
 */
static void __attribute__((unused)) pid_app_limit_integral(PID_T *pid, float min, float max)
{
    if (pid->integral > max)
    {
        pid->integral = max;
    }
    else if (pid->integral < min)
    {
        pid->integral = min;
    }
}

/**
 * @brief 初始化PID应用模块
 */
void pid_app_init(void)
{
    // 初始化左轮PID控制器
    pid_init(&pid_left,
             pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,
             0.0f, pid_params_left.out_max);

    // 初始化右轮PID控制器
    pid_init(&pid_right,
             pid_params_right.kp, pid_params_right.ki, pid_params_right.kd,
             0.0f, pid_params_right.out_max);

//    // 初始化电机控制
//    dual_motor_init();
    
    // 默认不启动PID控制
    pid_running = false;
}

/**
 * @brief 设置PID控制器目标速度
 * @param left_rpm 左轮目标转速(RPM)
 * @param right_rpm 右轮目标转速(RPM)
 */
void pid_app_set_target_speed(float left_rpm, float right_rpm)
{
#if DEBUG_PID
    printf("设置目标速度: 左轮=%.1f RPM, 右轮=%.1f RPM\n", left_rpm, right_rpm);
#endif
    target_left_rpm = left_rpm;
    target_right_rpm = right_rpm;

    // 更新PID控制器的目标值
    pid_set_target(&pid_left, target_left_rpm);
    pid_set_target(&pid_right, target_right_rpm);
}

/**
 * @brief 更新当前实际速度
 */
void pid_app_update_speed(void)
{
    // 从编码器获取当前速度
    const QEIApp_Data_t* qei_data = QEIApp_GetData();
    
    current_left_rpm = qei_data->left_signed_velocity;
    current_right_rpm = qei_data->right_signed_velocity;
}

/**
 * @brief 执行PID计算
 * 通过PID算法计算速度控制值
 */
void pid_app_calc(void)
{
    float output_left, output_right;

    // 更新当前速度
    pid_app_update_speed();

#if DEBUG_PID
    // 只在调试模式下计算并打印误差
    float error_left = target_left_rpm - current_left_rpm;
    float error_right = target_right_rpm - current_right_rpm;
    printf("误差 左轮=%.1f RPM, 右轮=%.1f RPM\n", error_left, error_right);
#endif

    // 使用增量式PID计算左轮输出
    output_left = pid_calculate_incremental(&pid_left, current_left_rpm);

    // 使用增量式PID计算右轮输出
    output_right = pid_calculate_incremental(&pid_right, current_right_rpm);

    // 限幅输出
    output_left = constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
    output_right = constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);
    
    my_left_motor_set_speed(output_left);
    my_right_motor_set_speed(output_right);

    // 获取最终的电机控制值
//    motor_left_speed = (uint8_t)fabsf(output_left);
//    motor_right_speed = (uint8_t)fabsf(output_right);

//    // 确定电机方向
//    motor_left_dir = (output_left >= 0) ? MOTOR_DIR_FORWARD : MOTOR_DIR_REVERSE;
//    motor_right_dir = (output_right >= 0) ? MOTOR_DIR_FORWARD : MOTOR_DIR_REVERSE;

//#if DEBUG_PID
//    printf("PID输出: 左轮=%d (方向=%d), 右轮=%d (方向=%d)\n", 
//           motor_left_speed, motor_left_dir, motor_right_speed, motor_right_dir);
//#endif

//    // 控制电机
//    left_motor_set_speed(motor_left_speed, motor_left_dir);
//    right_motor_set_speed(motor_right_speed, motor_right_dir);
}

/**
 * @brief PID控制任务函数
 * 由定时器回调或主循环调用，执行PID计算
 */
void pid_app_task(void)
{
    if (pid_running)
    {
        // 执行PID计算
        pid_app_calc();
    }
}

/**
 * @brief 启动PID控制
 */
void pid_app_start(void)
{
    // 如果PID控制已经在运行中，直接返回
    if (pid_running)
    {
        return;
    }

#if DEBUG_PID
    printf("启动PID控制\n");
#endif

    // 重置PID控制器
    pid_reset(&pid_left);
    pid_reset(&pid_right);

    // 设置运行标志
    pid_running = true;
}

/**
 * @brief 停止PID控制
 */
void pid_app_stop(void)
{
    // 如果PID控制已经停止，直接返回
    if (!pid_running)
    {
        return;
    }

#if DEBUG_PID
    printf("停止PID控制\n");
#endif

    // 停止电机
    car_stop();

    // 清除运行标志
    pid_running = false;
}

/**
 * @brief 设置左轮PID参数
 * @param params PID参数结构体
 */
void pid_app_set_left_params(PidParams_t params)
{
    pid_params_left = params;
    pid_set_params(&pid_left, params.kp, params.ki, params.kd);
    pid_set_limit(&pid_left, params.out_max);
}

/**
 * @brief 设置右轮PID参数
 * @param params PID参数结构体
 */
void pid_app_set_right_params(PidParams_t params)
{
    pid_params_right = params;
    pid_set_params(&pid_right, params.kp, params.ki, params.kd);
    pid_set_limit(&pid_right, params.out_max);
} 
