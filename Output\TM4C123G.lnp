--cpu=Cortex-M4.fp.sp
"..\..\output\adc.o"
"..\..\output\aes.o"
"..\..\output\can.o"
"..\..\output\comp.o"
"..\..\output\cpu.o"
"..\..\output\crc.o"
"..\..\output\des.o"
"..\..\output\eeprom.o"
"..\..\output\emac.o"
"..\..\output\epi.o"
"..\..\output\flash.o"
"..\..\output\fpu.o"
"..\..\output\gpio.o"
"..\..\output\hibernate.o"
"..\..\output\i2c.o"
"..\..\output\interrupt.o"
"..\..\output\lcd.o"
"..\..\output\mpu.o"
"..\..\output\pwm.o"
"..\..\output\qei.o"
"..\..\output\shamd5.o"
"..\..\output\ssi.o"
"..\..\output\sw_crc.o"
"..\..\output\sysctl.o"
"..\..\output\sysexc.o"
"..\..\output\systick.o"
"..\..\output\timer.o"
"..\..\output\uart.o"
"..\..\output\udma.o"
"..\..\output\usb.o"
"..\..\output\watchdog.o"
"..\..\output\main.o"
"..\..\output\uartstdio.o"
"..\..\output\startup_rvmdk.o"
"..\..\output\uart_app.o"
"..\..\output\scheduler.o"
"..\..\output\key_app.o"
"..\..\output\adc_app.o"
"..\..\output\myiic.o"
"..\..\output\icm20608.o"
"..\..\output\systicktime.o"
"..\..\output\imu.o"
"..\..\output\glcdfont.o"
"..\..\output\oled.o"
"..\..\output\ssd1306.o"
"..\..\output\oled_app.o"
"..\..\output\qei_app.o"
"..\..\output\pwm_app.o"
"..\..\output\pwm_debug.o"
"..\..\output\motor_app.o"
"..\..\output\pid.o"
"..\..\output\pid_app.o"
--library_type=microlib --strict --scatter "..\..\Output\TM4C123G.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "..\..\TM4C123G.map" -o ..\..\Output\TM4C123G.axf