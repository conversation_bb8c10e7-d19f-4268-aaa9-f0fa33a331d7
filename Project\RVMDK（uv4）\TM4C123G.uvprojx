<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>TIVA</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060960::V5.06 update 7 (build 960)::.\ARCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>MSPM0G3507</Device>
          <Vendor>Texas Instruments</Vendor>
          <PackID>TexasInstruments.MSPM0G1X0X_G3X0X_DFP.1.3.1</PackID>
          <PackURL>https://software-dl.ti.com/msp430/esd/MSPM0-CMSIS/MSPM0G1X0X_G3X0X/latest/exports/</PackURL>
          <Cpu>IRAM(0x20000000,0x00008000) IRAM2(0x20100000,0x00008000) IROM(0x00000000,0x00020000) IROM2(0x00400000,0x00020000) XRAM(0x20200000,0x00008000) XRAM2(0x20300000,0x00008000) CPUTYPE("Cortex-M0+") CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC8000 -FN1 -FF0MSPM0G1X0X_G3X0X_MAIN_128KB -FS00 -FL020000 -FP0($$Device:MSPM0G3507$02_Flash_Programming\FlashARM\MSPM0G1X0X_G3X0X_MAIN_128KB.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile></RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:MSPM0G3507$03_SVD\MSPM0G350X.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>..\..\Output\</OutputDirectory>
          <OutputName>TM4C123G</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>..\..\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>H:\code\ti_code\ti_xifeng\Keil5_disp_size_bar_v0.3.exe</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -MPU </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0+</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments>-MPU </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0+</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M0+"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>1</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>1</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>0</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x20000</Size>
              </IROM>
              <XRAM>
                <Type>1</Type>
                <StartAddress>0x20200000</StartAddress>
                <Size>0x8000</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x400000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x20200000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x20300000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x20100000</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>--c99</MiscControls>
              <Define>rvmdk PART_TM4C123GH6PZ TARGET_IS_BLIZZARD_RB1</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\Libraries\inc;..\..\Libraries\utils;..\..\Libraries\driverlib;..\..\User</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Libraries</GroupName>
          <Files>
            <File>
              <FileName>adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\adc.c</FilePath>
            </File>
            <File>
              <FileName>aes.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\aes.c</FilePath>
            </File>
            <File>
              <FileName>can.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\can.c</FilePath>
            </File>
            <File>
              <FileName>comp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\comp.c</FilePath>
            </File>
            <File>
              <FileName>cpu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\cpu.c</FilePath>
            </File>
            <File>
              <FileName>crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\crc.c</FilePath>
            </File>
            <File>
              <FileName>des.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\des.c</FilePath>
            </File>
            <File>
              <FileName>eeprom.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\eeprom.c</FilePath>
            </File>
            <File>
              <FileName>emac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\emac.c</FilePath>
            </File>
            <File>
              <FileName>epi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\epi.c</FilePath>
            </File>
            <File>
              <FileName>flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\flash.c</FilePath>
            </File>
            <File>
              <FileName>fpu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\fpu.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\gpio.c</FilePath>
            </File>
            <File>
              <FileName>hibernate.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\hibernate.c</FilePath>
            </File>
            <File>
              <FileName>i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\i2c.c</FilePath>
            </File>
            <File>
              <FileName>interrupt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\interrupt.c</FilePath>
            </File>
            <File>
              <FileName>lcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\lcd.c</FilePath>
            </File>
            <File>
              <FileName>mpu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\mpu.c</FilePath>
            </File>
            <File>
              <FileName>pwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\pwm.c</FilePath>
            </File>
            <File>
              <FileName>qei.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\qei.c</FilePath>
            </File>
            <File>
              <FileName>shamd5.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\shamd5.c</FilePath>
            </File>
            <File>
              <FileName>ssi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\ssi.c</FilePath>
            </File>
            <File>
              <FileName>sw_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\sw_crc.c</FilePath>
            </File>
            <File>
              <FileName>sysctl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\sysctl.c</FilePath>
            </File>
            <File>
              <FileName>sysexc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\sysexc.c</FilePath>
            </File>
            <File>
              <FileName>systick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\systick.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\timer.c</FilePath>
            </File>
            <File>
              <FileName>uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\uart.c</FilePath>
            </File>
            <File>
              <FileName>udma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\udma.c</FilePath>
            </File>
            <File>
              <FileName>usb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\usb.c</FilePath>
            </File>
            <File>
              <FileName>watchdog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\driverlib\watchdog.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>User</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\main.c</FilePath>
            </File>
            <File>
              <FileName>uartstdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\utils\uartstdio.c</FilePath>
            </File>
            <File>
              <FileName>startup_rvmdk.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\User\startup_rvmdk.S</FilePath>
            </File>
            <File>
              <FileName>uart_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\uart_app.c</FilePath>
            </File>
            <File>
              <FileName>system.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\system.h</FilePath>
            </File>
            <File>
              <FileName>scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\scheduler.c</FilePath>
            </File>
            <File>
              <FileName>key_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\key_app.c</FilePath>
            </File>
            <File>
              <FileName>adc_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\adc_app.c</FilePath>
            </File>
            <File>
              <FileName>myiic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\myiic.c</FilePath>
            </File>
            <File>
              <FileName>icm20608.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\icm20608.c</FilePath>
            </File>
            <File>
              <FileName>WP_DataType.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\WP_DataType.h</FilePath>
            </File>
            <File>
              <FileName>SystickTime.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\SystickTime.c</FilePath>
            </File>
            <File>
              <FileName>imu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\imu.c</FilePath>
            </File>
            <File>
              <FileName>glcdfont.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\glcdfont.c</FilePath>
            </File>
            <File>
              <FileName>OLED.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\OLED.c</FilePath>
            </File>
            <File>
              <FileName>ssd1306.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\ssd1306.c</FilePath>
            </File>
            <File>
              <FileName>oled_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\oled_app.c</FilePath>
            </File>
            <File>
              <FileName>qei_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\qei_app.c</FilePath>
            </File>
            <File>
              <FileName>pwm_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\pwm_app.c</FilePath>
            </File>
            <File>
              <FileName>pwm_debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\pwm_debug.c</FilePath>
            </File>
            <File>
              <FileName>motor_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\motor_app.c</FilePath>
            </File>
            <File>
              <FileName>pid.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\pid.c</FilePath>
            </File>
            <File>
              <FileName>pid.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\pid.h</FilePath>
            </File>
            <File>
              <FileName>pid_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\pid_app.c</FilePath>
            </File>
            <File>
              <FileName>pid_app.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\pid_app.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Doc</GroupName>
          <Files>
            <File>
              <FileName>readme.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Doc\readme.txt</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="5.4.0" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.7.0"/>
        <targetInfos>
          <targetInfo name="TIVA"/>
        </targetInfos>
      </component>
    </components>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>&lt;Project Info&gt;</LayName>
        <LayTarg>0</LayTarg>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
