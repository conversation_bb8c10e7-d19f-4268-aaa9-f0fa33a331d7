/**
 * @file motor_app.h
 * @brief 双轮电机控制模块头文件
 * <AUTHOR>
 * @copyright 米醋电子工作室
 */

#ifndef MOTOR_APP_H
#define MOTOR_APP_H

#include <stdint.h>
#include <stdbool.h>

// 电机方向定义
typedef enum
{
    MOTOR_DIR_FORWARD = 0, // 正向
    MOTOR_DIR_REVERSE = 1, // 反向
    MOTOR_DIR_STOP = 2     // 停止
} motor_direction_t;

/**
 * @brief 初始化双轮电机控制模块
 * @return true 初始化成功，false 初始化失败
 */
bool dual_motor_init(void);

/**
 * @brief 设置左轮电机速度和方向
 * @param speed 速度（0-100），越大转速越快
 * @param direction 电机方向
 * @return true 设置成功，false 设置失败
 */
bool left_motor_set_speed(uint8_t speed, motor_direction_t direction);

/**
 * @brief 设置右轮电机速度和方向
 * @param speed 速度（0-100），越大转速越快
 * @param direction 电机方向
 * @return true 设置成功，false 设置失败
 */
bool right_motor_set_speed(uint8_t speed, motor_direction_t direction);

/**
 * @brief 小车前进
 * @param speed 速度（0-100）
 * @return true 设置成功，false 设置失败
 */
bool car_forward(uint8_t speed);

/**
 * @brief 小车后退
 * @param speed 速度（0-100）
 * @return true 设置成功，false 设置失败
 */
bool car_backward(uint8_t speed);

/**
 * @brief 小车左转（左轮速度较低）
 * @param speed 速度（0-100）
 * @return true 设置成功，false 设置失败
 */
bool car_turn_left(uint8_t speed);

/**
 * @brief 小车右转（右轮速度较低）
 * @param speed 速度（0-100）
 * @return true 设置成功，false 设置失败
 */
bool car_turn_right(uint8_t speed);

/**
 * @brief 小车原地左转（左轮后退，右轮前进）
 * @param speed 速度（0-100）
 * @return true 设置成功，false 设置失败
 */
bool car_spin_left(uint8_t speed);

/**
 * @brief 小车原地右转（右轮后退，左轮前进）
 * @param speed 速度（0-100）
 * @return true 设置成功，false 设置失败
 */
bool car_spin_right(uint8_t speed);

/**
 * @brief 小车停止
 * @return true 设置成功，false 设置失败
 */
bool car_stop(void);

bool my_right_motor_set_speed(int16_t speed);
bool my_left_motor_set_speed(int16_t speed);

#endif /* MOTOR_APP_H */


