#include "scheduler.h"
#include "pid_app.h"


// 全局变量，用于存储任务数量
uint8_t task_num;

typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
} task_t;


// 静态任务数组，每个任务包含任务函数、执行周期（毫秒）、上次运行时间（毫秒）
static task_t scheduler_task[] =
{
//    {Uart_Proc, 10, 0}, // 添加一个任务，函数为 Uart_Proc，执行周期为 10 毫秒，初始上次运行时间为 0
		{Adc_Proc, 20, 0}, // 添加一个任务，函数为 Adc_Proc，执行周期为 20 毫秒，初始上次运行时间为 0
    {Key_Proc, 1, 0},
//		{Imu_Proc, 20, 0}, // 添加一个任务，函数为 Imu_Proc，执行周期为 20 毫秒，初始上次运行时间为 0
};

/**
 * @brief 初始化调度器
 * 初始化时，需要将任务数组中的元素个数存储到 task_num 中
 */
void scheduler_init(void)
{
    // 将任务数组中的元素个数存储到 task_num 中
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

/**
 * @brief 运行调度器
 * 遍历任务数组，判断每个任务是否需要执行，如果需要执行，则执行任务，并更新上次运行时间
 */
void scheduler_run(void)
{
    // 遍历任务数组
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 获取当前系统时间（毫秒）
        uint32_t now_time = systick;

        // 判断当前时间是否达到任务需要执行的时间
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            // 将上次运行时间更新为当前时间
            scheduler_task[i].last_run = now_time;

            // 执行任务
            scheduler_task[i].task_func();
        }
    }
}


