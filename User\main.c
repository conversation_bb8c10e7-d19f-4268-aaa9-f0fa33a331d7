#include "system.h"
#include "pid_app.h"

void usart_send(float roll, float pitch, float yaw, uint8_t fusion_sta)
{
    uint8_t buffer[15];   // 发送帧缓冲区
    uint8_t sumcheck = 0; // 校验和变量
    uint8_t addcheck = 0; // 附加校验变量
    uint8_t index = 0;    // 缓冲区索引

    // 将欧拉角转换为 int16，并放大100倍
    int16_t roll_int = (int16_t)(roll * 100.0f);
    int16_t pitch_int = (int16_t)(pitch * 100.0f);
    int16_t yaw_int = (int16_t)(yaw * 100.0f);

    // 帧头 (0xAB)
    buffer[index++] = 0xAB;
    // 源地址 (设置为 0xDC, 可作为飞控默认地址)
    buffer[index++] = 0xDC;
    // 目标地址 (0xFE, 广播地址)
    buffer[index++] = 0xFE;
    // 功能码 (ID: 0x03 表示飞控姿态，欧拉角格式)
    buffer[index++] = 0x03;
    // 数据长度 (7字节数据)
    buffer[index++] = 7;
    buffer[index++] = 0; // 数据长度高字节为0

    // 欧拉角数据 (int16, 角度放大100倍)
    buffer[index++] = (uint8_t)(roll_int & 0xFF);
    buffer[index++] = (uint8_t)((roll_int >> 8) & 0xFF);
    buffer[index++] = (uint8_t)(pitch_int & 0xFF);
    buffer[index++] = (uint8_t)((pitch_int >> 8) & 0xFF);
    buffer[index++] = (uint8_t)(yaw_int & 0xFF);
    buffer[index++] = (uint8_t)((yaw_int >> 8) & 0xFF);

    // 融合状态 (uint8)
    buffer[index++] = fusion_sta;

    // 计算校验和和附加校验 (从帧头开始到DATA结束)
    for (int i = 0; i < index; i++)
    {
        sumcheck += buffer[i];
        addcheck += sumcheck;
    }

    // 添加校验和和附加校验值
    buffer[index++] = sumcheck;
    buffer[index++] = addcheck;

    // 发送整个帧
    for (int i = 0; i < index; i++)
    {
        printf("%c", buffer[i]);
    }
}


// 配置两个PWM通道调试
pwm_debug_config_t pwm_config1 = {
    .channel = PWM_APP_CHANNEL_M0_0,
    .window_name = "pwm_multi",
    .debug_mode = PWM_DEBUG_MODE_LEVEL_COMBINED,
    .enabled = true};

pwm_debug_config_t pwm_config2 = {
    .channel = PWM_APP_CHANNEL_M0_1,
    .window_name = "pwm_multi",
    .debug_mode = PWM_DEBUG_MODE_LEVEL_COMBINED,
    .enabled = true};



pwm_debug_config_t *configs[] = {&pwm_config1, &pwm_config2};

uint64_t system_counter;

void pwm_proc()
{
    system_counter++;
    pwm_debug_report_multi(configs, 2, system_counter);
}

int main(void)
{
    ROM_FPUEnable();                                                                              // 使能浮点单元
    ROM_FPULazyStackingEnable();                                                                  // 启用延迟堆栈,降低中断响应延迟
    ROM_SysCtlClockSet(SYSCTL_SYSDIV_2_5 | SYSCTL_USE_PLL | SYSCTL_XTAL_16MHZ | SYSCTL_OSC_MAIN); // 设置系统时钟
    ROM_SysCtlPeripheralEnable(SYSCTL_PERIPH_GPIOF);
    ROM_GPIOPinTypeGPIOOutput(GPIO_PORTF_BASE, GPIO_PIN_5); // 蓝色
    ROM_GPIOPinTypeGPIOOutput(GPIO_PORTF_BASE, GPIO_PIN_6); // 绿色
    ROM_GPIOPinTypeGPIOOutput(GPIO_PORTF_BASE, GPIO_PIN_4); // 红色
    initTime();
    ConfigureUART(); // 初始化串口0
    ConfigureDMA();
    QEIApp_UnlockPF0();
    QEIApp_Init();
    ADC_DMA_Init();
    Key_Init();
    Init_I2C();
    ICM20608_Init();
    Time_init();
    OLED_Init();
    Init();
    dual_motor_init();
    pwm_debug_init(&pwm_config1);
    pwm_debug_init(&pwm_config2);
    
    // 初始化PID应用模块
    pid_app_init();
    // 设置目标转速
    pid_app_set_target_speed(50.0f, 50.0f);
    // 启动PID控制
//    left_motor_set_speed(50, 0);
//    right_motor_set_speed(50, 0);
//my_right_motor_set_speed(50);
    scheduler_init();
    while (1)
    {
        const QEIApp_Data_t* data = QEIApp_GetData();
        printf("%d,%d\r\n", 50, data->right_signed_velocity);
        scheduler_run();
    }
}
