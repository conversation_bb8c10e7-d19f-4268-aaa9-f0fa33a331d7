#include "adc_app.h"
#include "system.h"


// ADC基本配置
#define ADC_BASE              ADC0_BASE      			  // 使用的ADC模块
#define ADC_PERIPH            SYSCTL_PERIPH_ADC0    // ADC外设
#define ADC_SEQUENCE_NUM      0                			// ADC序列号，可选值：0/1/2/3
#define ADC_TRIGGER_SOURCE    ADC_TRIGGER_TIMER  		// ADC触发源
#define ADC_PRIORITY          0                			// ADC优先级

// ADC GPIO配置
#define ADC_GPIO_PERIPH_E     SYSCTL_PERIPH_GPIOE  // ADC使用的GPIO E外设
#define ADC_GPIO_PERIPH_D     SYSCTL_PERIPH_GPIOD  // ADC使用的GPIO D外设
#define ADC_GPIO_PORT_E       GPIO_PORTE_BASE      // ADC使用的GPIO E端口
#define ADC_GPIO_PORT_D       GPIO_PORTD_BASE      // ADC使用的GPIO D端口
#define ADC_PIN_VBAT          GPIO_PIN_3           // 电池电压检测引脚(PE3)
#define ADC_PIN_CH1           GPIO_PIN_0           // 通道1引脚(PD0)
#define ADC_PIN_CH2           GPIO_PIN_1           // 通道2引脚(PD1)
#define ADC_PIN_CH3           GPIO_PIN_2           // 通道3引脚(PD2)
#define ADC_PIN_CH4           GPIO_PIN_3           // 通道4引脚(PD3)

// ADC通道配置
#define ADC_VBAT_CHANNEL      ADC_CTL_CH0     // 电池电压检测通道
#define ADC_CHANNEL1          ADC_CTL_CH15    // 通道1
#define ADC_CHANNEL2          ADC_CTL_CH14    // 通道2
#define ADC_CHANNEL3          ADC_CTL_CH13    // 通道3
#define ADC_CHANNEL4          ADC_CTL_CH12    // 通道4

// ADC采样配置
#define ADC_OVERSAMPLE_RATE   8               // ADC硬件过采样倍数，可选值：0/2/4/8/16/32/64
#define ADC_SAMPLE_FREQ       10000           // 采样频率(Hz)

// ADC定时器配置
#define ADC_TIMER_BASE        TIMER2_BASE     			// 使用定时器2
#define ADC_TIMER             TIMER_A        			  // 使用定时器A
#define ADC_TIMER_PERIPH      SYSCTL_PERIPH_TIMER2  // 定时器外设

// ADC数据处理参数
#define ADC_FILTER_FACTOR     0.7f            // 滤波系数
#define ADC_SCALE_FACTOR      10.89f          // 电压转换系数
#define ADC_MAX_VALUE         4095.0f         // ADC最大值

// ADC DMA配置
#define ADC_DMA_CHANNEL       UDMA_CHANNEL_ADC0  	// DMA通道
#define ADC_BUFFER_SIZE       5               		// DMA缓冲区大小（通道数量）   
#define ADC_DMA_CONTROL       (UDMA_SIZE_32 | UDMA_SRC_INC_NONE | UDMA_DST_INC_32 | UDMA_ARB_1)  // DMA控制参数

// uDMA控制表 - 1024字节对齐
extern uint32_t controlTableSpace[256]; // DMA 内存空间
extern tDMAControlTable *controlTable;  // 指向控制表的指针

// ADC DMA双缓冲
uint32_t adcBuffer0[ADC_BUFFER_SIZE];
uint32_t adcBuffer1[ADC_BUFFER_SIZE];

// 每个通道的数据和滤波值
float ADC_Values[ADC_BUFFER_SIZE];  	// 存储各个通道的转换后的电压值
double ADC_Filters[ADC_BUFFER_SIZE];  // 每个通道的滤波值

float Battery_Voltage;

void Adc_Proc(void)
{
	/* 获取编码器数据 */
	const QEIApp_Data_t* data = QEIApp_GetData();
	
	/* 添加编码器用于控制调试信息输出频率 */
	//static int dump_counter = 0;
	
//	printf("QEI编码器速度: L:%d R:%d\n", data->left_signed_velocity, data->right_signed_velocity);
//  printf("QEI编码器RMP: L:%.2f R:%.2f\n", data->left_rpm, data->right_rpm);         

    // printf("QEI VEL: %u %u\n", (unsigned int)QEIVelocityGet(QEI1_BASE),
    //        (unsigned int)QEIVelocityGet(QEI0_BASE));
    // printf("QEI DIR: %d %d\n", QEIDirectionGet(QEI1_BASE), QEIDirectionGet(QEI0_BASE));
}

void ADC_Timer_Init(void)
{
    // 使能定时器外设
    SysCtlPeripheralEnable(SYSCTL_PERIPH_TIMER2);  // 这里定时器2
    while(!SysCtlPeripheralReady(SYSCTL_PERIPH_TIMER2));
    
    // 配置定时器
    TimerConfigure(ADC_TIMER_BASE, TIMER_CFG_PERIODIC);
    
    // 设置定时器周期，系统时钟/采样频率
    TimerLoadSet(ADC_TIMER_BASE, ADC_TIMER, SysCtlClockGet()/ADC_SAMPLE_FREQ);
    
    // 设置定时器触发ADC
    TimerControlTrigger(ADC_TIMER_BASE, ADC_TIMER, true);
    
    // 使能定时器
    TimerEnable(ADC_TIMER_BASE, ADC_TIMER);
}

void ADC0_Handler(void)
{
    ADCIntClear(ADC0_BASE, 0);
    
    // 检查DMA状态
    uint32_t priMode = uDMAChannelModeGet(UDMA_CHANNEL_ADC0 | UDMA_PRI_SELECT);
    uint32_t altMode = uDMAChannelModeGet(UDMA_CHANNEL_ADC0 | UDMA_ALT_SELECT);
    
    // 主缓冲区完成
    if(priMode == UDMA_MODE_STOP)
    {
        // 处理所有通道的数据
        for(int i = 0; i < ADC_BUFFER_SIZE; i++)
        {
            ADC_Filters[i] = ADC_FILTER_FACTOR * ADC_Filters[i] + 
                            ADC_SCALE_FACTOR * adcBuffer0[i] / ADC_MAX_VALUE;
            ADC_Values[i] = (float)ADC_Filters[i];
        }
        
        // 更新电池电压（第一个通道）
        Battery_Voltage = ADC_Values[0];
        
        // 重新配置主缓冲区传输
        uDMAChannelTransferSet(UDMA_CHANNEL_ADC0 | UDMA_PRI_SELECT,
                              UDMA_MODE_PINGPONG,
                              (void *)(ADC0_BASE + ADC_O_SSFIFO0),
                              adcBuffer0,
                              ADC_BUFFER_SIZE);
    }
    
    // 备用缓冲区完成
    if(altMode == UDMA_MODE_STOP)
    {
        // 处理所有通道的数据
        for(int i = 0; i < ADC_BUFFER_SIZE; i++)
        {
            ADC_Filters[i] = ADC_FILTER_FACTOR * ADC_Filters[i] + 
                            ADC_SCALE_FACTOR * adcBuffer1[i] / ADC_MAX_VALUE;
            ADC_Values[i] = (float)ADC_Filters[i];
        }
        
        // 更新电池电压（第一个通道）
        Battery_Voltage = ADC_Values[0];
        
        // 重新配置备用缓冲区传输
        uDMAChannelTransferSet(UDMA_CHANNEL_ADC0 | UDMA_ALT_SELECT,
                              UDMA_MODE_PINGPONG,
                              (void *)(ADC0_BASE + ADC_O_SSFIFO0),
                              adcBuffer1,
                              ADC_BUFFER_SIZE);
    }
}

void ADC_DMA_Init(void)
{
    // 使能相关时钟
    SysCtlPeripheralEnable(SYSCTL_PERIPH_ADC0);
    SysCtlPeripheralEnable(SYSCTL_PERIPH_UDMA);
    SysCtlPeripheralEnable(SYSCTL_PERIPH_GPIOE);
    SysCtlPeripheralEnable(SYSCTL_PERIPH_GPIOD);
    
    while(!SysCtlPeripheralReady(SYSCTL_PERIPH_ADC0) || 
          !SysCtlPeripheralReady(SYSCTL_PERIPH_UDMA));
    
    // 配置GPIO
    GPIOPinTypeADC(GPIO_PORTE_BASE, GPIO_PIN_3);
    GPIOPinTypeADC(GPIO_PORTD_BASE, GPIO_PIN_0);
    GPIOPinTypeADC(GPIO_PORTD_BASE, GPIO_PIN_1);
    GPIOPinTypeADC(GPIO_PORTD_BASE, GPIO_PIN_2);
    GPIOPinTypeADC(GPIO_PORTD_BASE, GPIO_PIN_3);
    
    // 初始化uDMA
    uDMAEnable();
    uDMAControlBaseSet(controlTable);
    
    // 配置ADC序列
    ADCSequenceConfigure(ADC0_BASE, ADC_SEQUENCE_NUM, ADC_TRIGGER_SOURCE, ADC_PRIORITY);
    ADCHardwareOversampleConfigure(ADC0_BASE, ADC_OVERSAMPLE_RATE);
    
    ADCSequenceStepConfigure(ADC0_BASE, ADC_SEQUENCE_NUM, 0, ADC_VBAT_CHANNEL);
    ADCSequenceStepConfigure(ADC0_BASE, ADC_SEQUENCE_NUM, 1, ADC_CHANNEL1);
    ADCSequenceStepConfigure(ADC0_BASE, ADC_SEQUENCE_NUM, 2, ADC_CHANNEL2);
    ADCSequenceStepConfigure(ADC0_BASE, ADC_SEQUENCE_NUM, 3, ADC_CHANNEL3);
    ADCSequenceStepConfigure(ADC0_BASE, ADC_SEQUENCE_NUM, 4, ADC_CHANNEL4 | ADC_CTL_END | ADC_CTL_IE);
    
    // 配置DMA通道
    uDMAChannelAttributeDisable(UDMA_CHANNEL_ADC0, 
                               UDMA_ATTR_HIGH_PRIORITY |
                               UDMA_ATTR_REQMASK);
                               
    // 配置主通道控制
    uDMAChannelControlSet(UDMA_CHANNEL_ADC0 | UDMA_PRI_SELECT,
                         ADC_DMA_CONTROL);
                         
    // 配置备用通道控制
    uDMAChannelControlSet(UDMA_CHANNEL_ADC0 | UDMA_ALT_SELECT,
                         ADC_DMA_CONTROL);
    
    // 配置主通道传输
    uDMAChannelTransferSet(UDMA_CHANNEL_ADC0 | UDMA_PRI_SELECT,
                          UDMA_MODE_PINGPONG,
                          (void *)(ADC0_BASE + ADC_O_SSFIFO0),
                          adcBuffer0,
                          ADC_BUFFER_SIZE);
                          
    // 配置备用通道传输
    uDMAChannelTransferSet(UDMA_CHANNEL_ADC0 | UDMA_ALT_SELECT,
                          UDMA_MODE_PINGPONG,
                          (void *)(ADC0_BASE + ADC_O_SSFIFO0),
                          adcBuffer1,
                          ADC_BUFFER_SIZE);
    
    // 使能DMA请求
    ADCSequenceDMAEnable(ADC0_BASE, 0);
    
    // 使能ADC序列和DMA通道
    ADCSequenceEnable(ADC0_BASE, 0);
    uDMAChannelEnable(UDMA_CHANNEL_ADC0);
    
    // 注册和使能中断
    ADCIntRegister(ADC0_BASE, 0, ADC0_Handler);
    ADCIntEnable(ADC0_BASE, 0);
    IntEnable(INT_ADC0SS0);
    
    // 初始化定时器
    ADC_Timer_Init();
}


