<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [..\..\Output\TM4C123G.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image ..\..\Output\TM4C123G.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Tue Jun 03 19:51:57 2025
<BR><P>
<H3>Maximum Stack Usage =        192 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
TIMER0A_Handler &rArr; MPU6050_Read_Data &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[2]">Reset_Handler</a>
 <LI><a href="#[3]">NmiSR</a>
 <LI><a href="#[4]">FaultISR</a>
 <LI><a href="#[5]">IntDefaultHandler</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[11]">SysCtlDelay</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[11]">SysCtlDelay</a><BR>
 <LI><a href="#[3]">NmiSR</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">NmiSR</a><BR>
 <LI><a href="#[4]">FaultISR</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">FaultISR</a><BR>
 <LI><a href="#[5]">IntDefaultHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">IntDefaultHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[ad]">ADC0_Handler</a> from adc_app.o(.text) referenced from adc_app.o(.text)
 <LI><a href="#[0]">Adc_Proc</a> from adc_app.o(.text) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[4]">FaultISR</a> from startup_rvmdk.o(RESET) referenced from startup_rvmdk.o(RESET)
 <LI><a href="#[5]">IntDefaultHandler</a> from startup_rvmdk.o(RESET) referenced 115 times from startup_rvmdk.o(RESET)
 <LI><a href="#[1]">Key_Proc</a> from key_app.o(.text) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[3]">NmiSR</a> from startup_rvmdk.o(RESET) referenced from startup_rvmdk.o(RESET)
 <LI><a href="#[a]">QEIApp_QEI0IntHandler</a> from qei_app.o(.text) referenced from qei_app.o(.text)
 <LI><a href="#[9]">QEIApp_QEI1IntHandler</a> from qei_app.o(.text) referenced from qei_app.o(.text)
 <LI><a href="#[2]">Reset_Handler</a> from startup_rvmdk.o(RESET) referenced from startup_rvmdk.o(RESET)
 <LI><a href="#[8]">SycTickHandler</a> from systicktime.o(.text) referenced from systicktime.o(.text)
 <LI><a href="#[9c]">TIMER0A_Handler</a> from uart_app.o(.text) referenced from uart_app.o(.text)
 <LI><a href="#[88]">UART0_IRQHandler</a> from uart_app.o(.text) referenced from uart_app.o(.text)
 <LI><a href="#[7]">_IntDefaultHandler</a> from interrupt.o(.text) referenced from interrupt.o(.text)
 <LI><a href="#[c]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0snprintf)
 <LI><a href="#[d]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0vsprintf)
 <LI><a href="#[b]">fputc</a> from uart_app.o(.text) referenced from printfa.o(i.__0printf)
 <LI><a href="#[6]">main</a> from main.o(.text) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[2]"></a>Reset_Handler</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_rvmdk.o(RESET))
<BR><BR>[Calls]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_rvmdk.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_Handler
</UL>

<P><STRONG><a name="[18a]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[f]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[178]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[18b]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[18c]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[18d]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[18e]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[18f]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[190]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[45]"></a>CPUcpsid</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, cpu.o(.emb_text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntMasterDisable
</UL>

<P><STRONG><a name="[191]"></a>CPUprimask</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, cpu.o(.emb_text), UNUSED)

<P><STRONG><a name="[43]"></a>CPUcpsie</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, cpu.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntMasterEnable
</UL>

<P><STRONG><a name="[56]"></a>CPUwfi</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, cpu.o(.emb_text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDeepSleep
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlSleep
</UL>

<P><STRONG><a name="[47]"></a>CPUbasepriSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, cpu.o(.emb_text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntPriorityMaskSet
</UL>

<P><STRONG><a name="[49]"></a>CPUbasepriGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, cpu.o(.emb_text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntPriorityMaskGet
</UL>

<P><STRONG><a name="[11]"></a>SysCtlDelay</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.emb_text))
<BR><BR>[Calls]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDelay
</UL>
<BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDelay
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlClockSet
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_Init
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C0
</UL>

<P><STRONG><a name="[12]"></a>ADCIntRegister</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = ADCIntRegister &rArr; IntRegister
</UL>
<BR>[Calls]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ADCIntNumberGet
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[16]"></a>ADCIntUnregister</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ADCIntNumberGet
</UL>

<P><STRONG><a name="[192]"></a>ADCIntDisable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[b8]"></a>ADCIntEnable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, adc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[193]"></a>ADCIntStatus</STRONG> (Thumb, 54 bytes, Stack size 12 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[ae]"></a>ADCIntClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
</UL>

<P><STRONG><a name="[b7]"></a>ADCSequenceEnable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, adc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[194]"></a>ADCSequenceDisable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[b3]"></a>ADCSequenceConfigure</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ADCSequenceConfigure
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[b5]"></a>ADCSequenceStepConfigure</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADCSequenceStepConfigure
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[195]"></a>ADCSequenceOverflow</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[196]"></a>ADCSequenceOverflowClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[197]"></a>ADCSequenceUnderflow</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[198]"></a>ADCSequenceUnderflowClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[199]"></a>ADCSequenceDataGet</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[19a]"></a>ADCProcessorTrigger</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[19b]"></a>ADCSoftwareOversampleConfigure</STRONG> (Thumb, 24 bytes, Stack size 12 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[19c]"></a>ADCSoftwareOversampleStepConfigure</STRONG> (Thumb, 110 bytes, Stack size 12 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[19d]"></a>ADCSoftwareOversampleDataGet</STRONG> (Thumb, 54 bytes, Stack size 20 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[b4]"></a>ADCHardwareOversampleConfigure</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, adc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[19e]"></a>ADCComparatorConfigure</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[19f]"></a>ADCComparatorRegionSet</STRONG> (Thumb, 16 bytes, Stack size 12 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1a0]"></a>ADCComparatorReset</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1a1]"></a>ADCComparatorIntDisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1a2]"></a>ADCComparatorIntEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1a3]"></a>ADCComparatorIntStatus</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1a4]"></a>ADCComparatorIntClear</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1a5]"></a>ADCIntDisableEx</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1a6]"></a>ADCIntEnableEx</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1a7]"></a>ADCIntStatusEx</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1a8]"></a>ADCIntClearEx</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1a9]"></a>ADCReferenceSet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1aa]"></a>ADCReferenceGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1ab]"></a>ADCPhaseDelaySet</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1ac]"></a>ADCPhaseDelayGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[b6]"></a>ADCSequenceDMAEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, adc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[1ad]"></a>ADCSequenceDMADisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1ae]"></a>ADCBusy</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, adc.o(.text), UNUSED)

<P><STRONG><a name="[1d]"></a>GPIODirModeSet</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, gpio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeCIR
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeLEDSeq
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeKBColumn
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeKBRow
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeWakeLow
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeWakeHigh
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeUSBDigital
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeUSBAnalog
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeUART
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeTimer
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeSSI
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeQEI
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypePWM
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypePECITx
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypePECIRx
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeLPC
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeLCD
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2CSCL
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2C
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOOutputOD
<LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOOutput
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOInput
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeFan
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeEthernetMII
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeEthernetLED
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeEPI
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeComparator
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeCAN
<LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeADC
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
</UL>

<P><STRONG><a name="[1af]"></a>GPIODirModeGet</STRONG> (Thumb, 46 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1b0]"></a>GPIOIntTypeSet</STRONG> (Thumb, 102 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1b1]"></a>GPIOIntTypeGet</STRONG> (Thumb, 84 bytes, Stack size 20 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1e]"></a>GPIOPadConfigSet</STRONG> (Thumb, 342 bytes, Stack size 20 bytes, gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIOPadConfigSet
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeCIR
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeLEDSeq
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeKBColumn
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeKBRow
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeWakeLow
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeWakeHigh
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeUSBDigital
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeUSBAnalog
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeUART
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeTimer
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeSSI
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeQEI
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypePWM
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypePECITx
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypePECIRx
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeLPC
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeLCD
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2CSCL
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2C
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOOutputOD
<LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOOutput
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOInput
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeFan
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeEthernetMII
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeEthernetLED
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeEPI
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeComparator
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeCAN
<LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeADC
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
</UL>

<P><STRONG><a name="[1b2]"></a>GPIOPadConfigGet</STRONG> (Thumb, 206 bytes, Stack size 20 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1b3]"></a>GPIOIntEnable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1b4]"></a>GPIOIntDisable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1b5]"></a>GPIOIntStatus</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1b6]"></a>GPIOIntClear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[19]"></a>GPIOIntRegister</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_GPIOIntNumberGet
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
</UL>

<P><STRONG><a name="[1b]"></a>GPIOIntUnregister</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_GPIOIntNumberGet
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
</UL>

<P><STRONG><a name="[a8]"></a>GPIOPinRead</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gpio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Read
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
</UL>

<P><STRONG><a name="[a9]"></a>GPIOPinWrite</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gpio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init_Fast
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLEDInit
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrCmd
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrDat
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_IIC_Byte
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_NAck
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Ack
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_IIC_Stop
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_IIC_Start
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
</UL>

<P><STRONG><a name="[1c]"></a>GPIOPinTypeADC</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIOPinTypeADC &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[1f]"></a>GPIOPinTypeCAN</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[20]"></a>GPIOPinTypeComparator</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[21]"></a>GPIOPinTypeEPI</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[22]"></a>GPIOPinTypeEthernetLED</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[23]"></a>GPIOPinTypeEthernetMII</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[24]"></a>GPIOPinTypeFan</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[25]"></a>GPIOPinTypeGPIOInput</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDA_IN
</UL>

<P><STRONG><a name="[26]"></a>GPIOPinTypeGPIOOutput</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIOPinTypeGPIOOutput &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init_Fast
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDA_OUT
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
</UL>

<P><STRONG><a name="[27]"></a>GPIOPinTypeGPIOOutputOD</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[28]"></a>GPIOPinTypeI2C</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIOPinTypeI2C &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C0
</UL>

<P><STRONG><a name="[29]"></a>GPIOPinTypeI2CSCL</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIOPinTypeI2CSCL &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C0
</UL>

<P><STRONG><a name="[2a]"></a>GPIOPinTypeLCD</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[2b]"></a>GPIOPinTypeLPC</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[2c]"></a>GPIOPinTypePECIRx</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[2d]"></a>GPIOPinTypePECITx</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[2e]"></a>GPIOPinTypePWM</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIOPinTypePWM &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_Init
</UL>

<P><STRONG><a name="[2f]"></a>GPIOPinTypeQEI</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GPIOPinTypeQEI &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
</UL>

<P><STRONG><a name="[30]"></a>GPIOPinTypeSSI</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[31]"></a>GPIOPinTypeTimer</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[32]"></a>GPIOPinTypeUART</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[33]"></a>GPIOPinTypeUSBAnalog</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[34]"></a>GPIOPinTypeUSBDigital</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[35]"></a>GPIOPinTypeWakeHigh</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[36]"></a>GPIOPinTypeWakeLow</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[37]"></a>GPIOPinTypeKBRow</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[38]"></a>GPIOPinTypeKBColumn</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[39]"></a>GPIOPinTypeLEDSeq</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[3a]"></a>GPIOPinTypeCIR</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
</UL>

<P><STRONG><a name="[1b7]"></a>GPIOPinWakeStatus</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[b9]"></a>GPIOPinConfigure</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GPIOPinConfigure
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_Init
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C0
</UL>

<P><STRONG><a name="[1b8]"></a>GPIODMATriggerEnable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1b9]"></a>GPIODMATriggerDisable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1ba]"></a>GPIOADCTriggerEnable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[1bb]"></a>GPIOADCTriggerDisable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(.text), UNUSED)

<P><STRONG><a name="[3c]"></a>I2CMasterEnable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterInitExpClk
</UL>

<P><STRONG><a name="[3b]"></a>I2CMasterInitExpClk</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, i2c.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2CMasterInitExpClk
</UL>
<BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C0
</UL>

<P><STRONG><a name="[3e]"></a>I2CSlaveEnable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CSlaveInit
</UL>

<P><STRONG><a name="[3d]"></a>I2CSlaveInit</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CSlaveEnable
</UL>

<P><STRONG><a name="[1bc]"></a>I2CSlaveAddressSet</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1bd]"></a>I2CMasterDisable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1be]"></a>I2CSlaveDisable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[3f]"></a>I2CIntRegister</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_I2CIntNumberGet
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
</UL>

<P><STRONG><a name="[41]"></a>I2CIntUnregister</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, i2c.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_I2CIntNumberGet
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
</UL>

<P><STRONG><a name="[1bf]"></a>I2CMasterIntEnable</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1c0]"></a>I2CMasterIntEnableEx</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1c1]"></a>I2CSlaveIntEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1c2]"></a>I2CSlaveIntEnableEx</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1c3]"></a>I2CMasterIntDisable</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1c4]"></a>I2CMasterIntDisableEx</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1c5]"></a>I2CSlaveIntDisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1c6]"></a>I2CSlaveIntDisableEx</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1c7]"></a>I2CMasterIntStatus</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1c8]"></a>I2CMasterIntStatusEx</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1c9]"></a>I2CSlaveIntStatus</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1ca]"></a>I2CSlaveIntStatusEx</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1cb]"></a>I2CMasterIntClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1cc]"></a>I2CMasterIntClearEx</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1cd]"></a>I2CSlaveIntClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1ce]"></a>I2CSlaveIntClearEx</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[bb]"></a>I2CMasterSlaveAddrSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0ReadData
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0Read
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0WriteData
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cReadData
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWriteData
</UL>

<P><STRONG><a name="[1cf]"></a>I2CMasterLineStateGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[be]"></a>I2CMasterBusy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0ReadData
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0Read
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0WriteData
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cReadData
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWriteData
</UL>

<P><STRONG><a name="[1d0]"></a>I2CMasterBusBusy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[bd]"></a>I2CMasterControl</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0ReadData
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0Read
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0WriteData
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cReadData
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWriteData
</UL>

<P><STRONG><a name="[1d1]"></a>I2CMasterErr</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[bc]"></a>I2CMasterDataPut</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0ReadData
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0Read
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0WriteData
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cReadData
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWriteData
</UL>

<P><STRONG><a name="[c0]"></a>I2CMasterDataGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0ReadData
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0Read
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cReadData
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
</UL>

<P><STRONG><a name="[1d2]"></a>I2CMasterTimeoutSet</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1d3]"></a>I2CSlaveACKOverride</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1d4]"></a>I2CSlaveACKValueSet</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1d5]"></a>I2CSlaveStatus</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1d6]"></a>I2CSlaveDataPut</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1d7]"></a>I2CSlaveDataGet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1d8]"></a>I2CTxFIFOConfigSet</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1d9]"></a>I2CTxFIFOFlush</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1da]"></a>I2CRxFIFOConfigSet</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1db]"></a>I2CRxFIFOFlush</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1dc]"></a>I2CFIFOStatus</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1dd]"></a>I2CFIFODataPut</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1de]"></a>I2CFIFODataPutNonBlocking</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1df]"></a>I2CFIFODataGet</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1e0]"></a>I2CFIFODataGetNonBlocking</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1e1]"></a>I2CMasterBurstLengthSet</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1e2]"></a>I2CMasterBurstCountGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1e3]"></a>I2CMasterGlitchFilterConfigSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1e4]"></a>I2CSlaveFIFOEnable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[1e5]"></a>I2CSlaveFIFODisable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, i2c.o(.text), UNUSED)

<P><STRONG><a name="[42]"></a>IntMasterEnable</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, interrupt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IntMasterEnable
</UL>
<BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CPUcpsie
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
</UL>

<P><STRONG><a name="[44]"></a>IntMasterDisable</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, interrupt.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CPUcpsid
</UL>

<P><STRONG><a name="[14]"></a>IntRegister</STRONG> (Thumb, 52 bytes, Stack size 12 bytes, interrupt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = IntRegister
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CIntRegister
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOIntRegister
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntRegister
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAIntRegister
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntRegister
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntRegister
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickIntRegister
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlIntRegister
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIIntRegister
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMFaultIntRegister
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMGenIntRegister
</UL>

<P><STRONG><a name="[18]"></a>IntUnregister</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CIntUnregister
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOIntUnregister
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntUnregister
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAIntUnregister
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntUnregister
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntUnregister
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickIntUnregister
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlIntUnregister
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIIntUnregister
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMFaultIntUnregister
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMGenIntUnregister
</UL>

<P><STRONG><a name="[1e6]"></a>IntPriorityGroupingSet</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[1e7]"></a>IntPriorityGroupingGet</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[9a]"></a>IntPrioritySet</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, interrupt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IntPrioritySet
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
</UL>

<P><STRONG><a name="[1e8]"></a>IntPriorityGet</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[15]"></a>IntEnable</STRONG> (Thumb, 120 bytes, Stack size 0 bytes, interrupt.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CIntRegister
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOIntRegister
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntRegister
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAIntRegister
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntRegister
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntRegister
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlIntRegister
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIIntRegister
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMFaultIntRegister
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMGenIntRegister
</UL>

<P><STRONG><a name="[17]"></a>IntDisable</STRONG> (Thumb, 120 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CIntUnregister
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOIntUnregister
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntUnregister
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAIntUnregister
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntUnregister
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntUnregister
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlIntUnregister
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIIntUnregister
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMFaultIntUnregister
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMGenIntUnregister
</UL>

<P><STRONG><a name="[1e9]"></a>IntIsEnabled</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[1ea]"></a>IntPendSet</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[1eb]"></a>IntPendClear</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[46]"></a>IntPriorityMaskSet</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, interrupt.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CPUbasepriSet
</UL>

<P><STRONG><a name="[48]"></a>IntPriorityMaskGet</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupt.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CPUbasepriGet
</UL>

<P><STRONG><a name="[1ec]"></a>IntTrigger</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, interrupt.o(.text), UNUSED)

<P><STRONG><a name="[13e]"></a>PWMGenConfigure</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, pwm.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PWMGenConfigure
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_Init
</UL>

<P><STRONG><a name="[13f]"></a>PWMGenPeriodSet</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, pwm.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_Init
</UL>

<P><STRONG><a name="[146]"></a>PWMGenPeriodGet</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, pwm.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_get_duty
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_SetDuty
</UL>

<P><STRONG><a name="[140]"></a>PWMGenEnable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, pwm.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_Init
</UL>

<P><STRONG><a name="[1ed]"></a>PWMGenDisable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[144]"></a>PWMPulseWidthSet</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, pwm.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = PWMPulseWidthSet
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_OutputMulti
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_SetServo
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_SetDuty
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_SetPulseWidth
</UL>

<P><STRONG><a name="[14b]"></a>PWMPulseWidthGet</STRONG> (Thumb, 40 bytes, Stack size 12 bytes, pwm.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_get_duty
</UL>

<P><STRONG><a name="[1ee]"></a>PWMDeadBandEnable</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1ef]"></a>PWMDeadBandDisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1f0]"></a>PWMSyncUpdate</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1f1]"></a>PWMSyncTimeBase</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[141]"></a>PWMOutputState</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, pwm.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_Init
</UL>

<P><STRONG><a name="[1f2]"></a>PWMOutputInvert</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1f3]"></a>PWMOutputFaultLevel</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1f4]"></a>PWMOutputFault</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[4a]"></a>PWMGenIntRegister</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, pwm.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_PWMGenIntNumberGet
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
</UL>

<P><STRONG><a name="[4c]"></a>PWMGenIntUnregister</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, pwm.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_PWMGenIntNumberGet
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
</UL>

<P><STRONG><a name="[4d]"></a>PWMFaultIntRegister</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, pwm.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_PWMFaultIntNumberGet
</UL>

<P><STRONG><a name="[4f]"></a>PWMFaultIntUnregister</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, pwm.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_PWMFaultIntNumberGet
</UL>

<P><STRONG><a name="[1f5]"></a>PWMGenIntTrigEnable</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1f6]"></a>PWMGenIntTrigDisable</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1f7]"></a>PWMGenIntStatus</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1f8]"></a>PWMGenIntClear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1f9]"></a>PWMIntEnable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1fa]"></a>PWMIntDisable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1fb]"></a>PWMFaultIntClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1fc]"></a>PWMIntStatus</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1fd]"></a>PWMFaultIntClearExt</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1fe]"></a>PWMGenFaultConfigure</STRONG> (Thumb, 20 bytes, Stack size 12 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[1ff]"></a>PWMGenFaultTriggerSet</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[200]"></a>PWMGenFaultTriggerGet</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[201]"></a>PWMGenFaultStatus</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[202]"></a>PWMGenFaultClear</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[203]"></a>PWMClockSet</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[204]"></a>PWMClockGet</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[205]"></a>PWMOutputUpdateMode</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, pwm.o(.text), UNUSED)

<P><STRONG><a name="[13a]"></a>QEIEnable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, qei.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
</UL>

<P><STRONG><a name="[134]"></a>QEIDisable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, qei.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
</UL>

<P><STRONG><a name="[136]"></a>QEIConfigure</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, qei.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
</UL>

<P><STRONG><a name="[206]"></a>QEIPositionGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, qei.o(.text), UNUSED)

<P><STRONG><a name="[137]"></a>QEIPositionSet</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, qei.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
</UL>

<P><STRONG><a name="[132]"></a>QEIDirectionGet</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, qei.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_QEI1IntHandler
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_QEI0IntHandler
</UL>

<P><STRONG><a name="[207]"></a>QEIErrorGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, qei.o(.text), UNUSED)

<P><STRONG><a name="[139]"></a>QEIVelocityEnable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, qei.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
</UL>

<P><STRONG><a name="[135]"></a>QEIVelocityDisable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, qei.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
</UL>

<P><STRONG><a name="[138]"></a>QEIVelocityConfigure</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, qei.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
</UL>

<P><STRONG><a name="[133]"></a>QEIVelocityGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, qei.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_QEI1IntHandler
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_QEI0IntHandler
</UL>

<P><STRONG><a name="[50]"></a>QEIIntRegister</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, qei.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = QEIIntRegister &rArr; IntRegister
</UL>
<BR>[Calls]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_QEIIntNumberGet
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
</UL>

<P><STRONG><a name="[52]"></a>QEIIntUnregister</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, qei.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_QEIIntNumberGet
</UL>

<P><STRONG><a name="[13b]"></a>QEIIntEnable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, qei.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
</UL>

<P><STRONG><a name="[208]"></a>QEIIntDisable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, qei.o(.text), UNUSED)

<P><STRONG><a name="[209]"></a>QEIIntStatus</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, qei.o(.text), UNUSED)

<P><STRONG><a name="[131]"></a>QEIIntClear</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, qei.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_QEI1IntHandler
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_QEI0IntHandler
</UL>

<P><STRONG><a name="[20a]"></a>SysCtlSRAMSizeGet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[20b]"></a>SysCtlFlashSizeGet</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[20c]"></a>SysCtlFlashSectorSizeGet</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[20d]"></a>SysCtlPeripheralPresent</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[90]"></a>SysCtlPeripheralReady</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, sysctl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[20e]"></a>SysCtlPeripheralPowerOn</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[20f]"></a>SysCtlPeripheralPowerOff</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[210]"></a>SysCtlPeripheralReset</STRONG> (Thumb, 106 bytes, Stack size 12 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[8f]"></a>SysCtlPeripheralEnable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysCtlPeripheralEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init_Fast
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C0
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[211]"></a>SysCtlPeripheralDisable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[212]"></a>SysCtlPeripheralSleepEnable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[213]"></a>SysCtlPeripheralSleepDisable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[214]"></a>SysCtlPeripheralDeepSleepEnable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[215]"></a>SysCtlPeripheralDeepSleepDisable</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[216]"></a>SysCtlPeripheralClockGating</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[53]"></a>SysCtlIntRegister</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
</UL>

<P><STRONG><a name="[54]"></a>SysCtlIntUnregister</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
</UL>

<P><STRONG><a name="[217]"></a>SysCtlIntEnable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[218]"></a>SysCtlIntDisable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[219]"></a>SysCtlIntClear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[21a]"></a>SysCtlIntStatus</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[21b]"></a>SysCtlLDOSleepSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[21c]"></a>SysCtlLDOSleepGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[21d]"></a>SysCtlLDODeepSleepSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[21e]"></a>SysCtlLDODeepSleepGet</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[21f]"></a>SysCtlSleepPowerSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[220]"></a>SysCtlDeepSleepPowerSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[221]"></a>SysCtlReset</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[55]"></a>SysCtlSleep</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CPUwfi
</UL>

<P><STRONG><a name="[57]"></a>SysCtlDeepSleep</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CPUwfi
</UL>

<P><STRONG><a name="[222]"></a>SysCtlResetCauseGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[223]"></a>SysCtlResetCauseClear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[224]"></a>SysCtlMOSCConfigSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[225]"></a>SysCtlPIOSCCalibrate</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[226]"></a>SysCtlResetBehaviorSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[227]"></a>SysCtlResetBehaviorGet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[58]"></a>SysCtlClockFreqSet</STRONG> (Thumb, 646 bytes, Stack size 48 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SysCtlFrequencyGet
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SysCtlMemTimingGet
</UL>

<P><STRONG><a name="[5b]"></a>SysCtlClockSet</STRONG> (Thumb, 362 bytes, Stack size 24 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDelay
</UL>

<P><STRONG><a name="[a3]"></a>SysCtlClockGet</STRONG> (Thumb, 244 bytes, Stack size 16 bytes, sysctl.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SysCtlClockGet
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initTime
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[228]"></a>SysCtlDeepSleepClockSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[5c]"></a>SysCtlDeepSleepClockConfigSet</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SysCtlMemTimingGet
</UL>

<P><STRONG><a name="[13d]"></a>SysCtlPWMClockSet</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, sysctl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_Init
</UL>

<P><STRONG><a name="[229]"></a>SysCtlPWMClockGet</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[22a]"></a>SysCtlADCSpeedSet</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[22b]"></a>SysCtlADCSpeedGet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[22c]"></a>SysCtlGPIOAHBEnable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[22d]"></a>SysCtlGPIOAHBDisable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[22e]"></a>SysCtlUSBPLLEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[22f]"></a>SysCtlUSBPLLDisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[230]"></a>SysCtlVoltageEventConfig</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[231]"></a>SysCtlVoltageEventStatus</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[232]"></a>SysCtlVoltageEventClear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[233]"></a>SysCtlNMIStatus</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[234]"></a>SysCtlNMIClear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[235]"></a>SysCtlClockOutConfig</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[236]"></a>SysCtlAltClkConfig</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)

<P><STRONG><a name="[cf]"></a>SysTickEnable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initTime
</UL>

<P><STRONG><a name="[237]"></a>SysTickDisable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(.text), UNUSED)

<P><STRONG><a name="[5d]"></a>SysTickIntRegister</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, systick.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SysTickIntRegister &rArr; IntRegister
</UL>
<BR>[Calls]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initTime
</UL>

<P><STRONG><a name="[5e]"></a>SysTickIntUnregister</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, systick.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
</UL>

<P><STRONG><a name="[ce]"></a>SysTickIntEnable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initTime
</UL>

<P><STRONG><a name="[238]"></a>SysTickIntDisable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(.text), UNUSED)

<P><STRONG><a name="[cd]"></a>SysTickPeriodSet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, systick.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initTime
</UL>

<P><STRONG><a name="[239]"></a>SysTickPeriodGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, systick.o(.text), UNUSED)

<P><STRONG><a name="[23a]"></a>SysTickValueGet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, systick.o(.text), UNUSED)

<P><STRONG><a name="[a6]"></a>TimerEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[23b]"></a>TimerDisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[a2]"></a>TimerConfigure</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[23c]"></a>TimerControlLevel</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[ac]"></a>TimerControlTrigger</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TimerControlTrigger
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[23d]"></a>TimerControlEvent</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[23e]"></a>TimerControlStall</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[23f]"></a>TimerControlWaitOnTrigger</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[240]"></a>TimerRTCEnable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[241]"></a>TimerRTCDisable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[242]"></a>TimerClockSourceSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[243]"></a>TimerClockSourceGet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[244]"></a>TimerPrescaleSet</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[245]"></a>TimerPrescaleGet</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[246]"></a>TimerPrescaleMatchSet</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[247]"></a>TimerPrescaleMatchGet</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[a4]"></a>TimerLoadSet</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>

<P><STRONG><a name="[248]"></a>TimerLoadGet</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[249]"></a>TimerLoadSet64</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[24a]"></a>TimerLoadGet64</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[24b]"></a>TimerValueGet</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[24c]"></a>TimerValueGet64</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[24d]"></a>TimerMatchSet</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[24e]"></a>TimerMatchGet</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[24f]"></a>TimerMatchSet64</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[250]"></a>TimerMatchGet64</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[5f]"></a>TimerIntRegister</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = TimerIntRegister &rArr; _TimerIntNumberGet
</UL>
<BR>[Calls]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_TimerIntNumberGet
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
</UL>

<P><STRONG><a name="[61]"></a>TimerIntUnregister</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, timer.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_TimerIntNumberGet
</UL>

<P><STRONG><a name="[a5]"></a>TimerIntEnable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
</UL>

<P><STRONG><a name="[251]"></a>TimerIntDisable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[252]"></a>TimerIntStatus</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[a1]"></a>TimerIntClear</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, timer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0A_Handler
</UL>

<P><STRONG><a name="[253]"></a>TimerSynchronize</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[254]"></a>TimerADCEventSet</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[255]"></a>TimerADCEventGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[256]"></a>TimerDMAEventSet</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[257]"></a>TimerDMAEventGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, timer.o(.text), UNUSED)

<P><STRONG><a name="[258]"></a>UARTParityModeSet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[259]"></a>UARTParityModeGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[25a]"></a>UARTFIFOLevelSet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[25b]"></a>UARTFIFOLevelGet</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[64]"></a>UARTEnable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTConfigSetExpClk
</UL>

<P><STRONG><a name="[63]"></a>UARTDisable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTConfigSetExpClk
</UL>

<P><STRONG><a name="[62]"></a>UARTConfigSetExpClk</STRONG> (Thumb, 76 bytes, Stack size 20 bytes, uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTDisable
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTEnable
</UL>

<P><STRONG><a name="[25c]"></a>UARTConfigGetExpClk</STRONG> (Thumb, 44 bytes, Stack size 20 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[99]"></a>UARTFIFOEnable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
</UL>

<P><STRONG><a name="[25d]"></a>UARTFIFODisable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[25e]"></a>UARTEnableSIR</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[25f]"></a>UARTDisableSIR</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[260]"></a>UARTSmartCardEnable</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[261]"></a>UARTSmartCardDisable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[262]"></a>UARTModemControlSet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[263]"></a>UARTModemControlClear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[264]"></a>UARTModemControlGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[265]"></a>UARTModemStatusGet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[266]"></a>UARTFlowControlSet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[267]"></a>UARTFlowControlGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[268]"></a>UARTTxIntModeSet</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[269]"></a>UARTTxIntModeGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[26a]"></a>UARTCharsAvail</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[26b]"></a>UARTSpaceAvail</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[26c]"></a>UARTCharGetNonBlocking</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[87]"></a>UARTCharGet</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fgetc
</UL>

<P><STRONG><a name="[26d]"></a>UARTCharPutNonBlocking</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[85]"></a>UARTCharPut</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[26e]"></a>UARTBreakCtl</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[26f]"></a>UARTBusy</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[65]"></a>UARTIntRegister</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = UARTIntRegister &rArr; IntRegister
</UL>
<BR>[Calls]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_UARTIntNumberGet
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
</UL>

<P><STRONG><a name="[67]"></a>UARTIntUnregister</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, uart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_UARTIntNumberGet
</UL>

<P><STRONG><a name="[9b]"></a>UARTIntEnable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
</UL>

<P><STRONG><a name="[270]"></a>UARTIntDisable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[89]"></a>UARTIntStatus</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[8a]"></a>UARTIntClear</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[271]"></a>UARTDMAEnable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[272]"></a>UARTDMADisable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[273]"></a>UARTRxErrorGet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[274]"></a>UARTRxErrorClear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[98]"></a>UARTClockSourceSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
</UL>

<P><STRONG><a name="[275]"></a>UARTClockSourceGet</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[276]"></a>UART9BitEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[277]"></a>UART9BitDisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[278]"></a>UART9BitAddrSet</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[279]"></a>UART9BitAddrSend</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, uart.o(.text), UNUSED)

<P><STRONG><a name="[91]"></a>uDMAEnable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, udma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[27a]"></a>uDMADisable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[27b]"></a>uDMAErrorStatusGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[27c]"></a>uDMAErrorStatusClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[96]"></a>uDMAChannelEnable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, udma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[27d]"></a>uDMAChannelDisable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[27e]"></a>uDMAChannelIsEnabled</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[92]"></a>uDMAControlBaseSet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, udma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[27f]"></a>uDMAControlBaseGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[280]"></a>uDMAControlAlternateBaseGet</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[281]"></a>uDMAChannelRequest</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[94]"></a>uDMAChannelAttributeEnable</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, udma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
</UL>

<P><STRONG><a name="[93]"></a>uDMAChannelAttributeDisable</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, udma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[282]"></a>uDMAChannelAttributeGet</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[95]"></a>uDMAChannelControlSet</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, udma.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = uDMAChannelControlSet
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[8c]"></a>uDMAChannelTransferSet</STRONG> (Thumb, 170 bytes, Stack size 28 bytes, udma.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = uDMAChannelTransferSet
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
</UL>

<P><STRONG><a name="[283]"></a>uDMAChannelScatterGatherSet</STRONG> (Thumb, 82 bytes, Stack size 20 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[284]"></a>uDMAChannelSizeGet</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[8b]"></a>uDMAChannelModeGet</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, udma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
</UL>

<P><STRONG><a name="[285]"></a>uDMAChannelSelectSecondary</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[286]"></a>uDMAChannelSelectDefault</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[68]"></a>uDMAIntRegister</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, udma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntRegister
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
</UL>

<P><STRONG><a name="[69]"></a>uDMAIntUnregister</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, udma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntUnregister
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDisable
</UL>

<P><STRONG><a name="[287]"></a>uDMAIntStatus</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[288]"></a>uDMAIntClear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[289]"></a>uDMAChannelAssign</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, udma.o(.text), UNUSED)

<P><STRONG><a name="[6a]"></a>usart_send</STRONG> (Thumb, 302 bytes, Stack size 72 bytes, main.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[6c]"></a>pwm_proc</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, main.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_report_multi
</UL>

<P><STRONG><a name="[6]"></a>main</STRONG> (Thumb, 196 bytes, Stack size 0 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = main &rArr; dual_motor_init &rArr; PWM_APP_SetDuty &rArr; PWM_APP_GetChannelConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_init
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_set_target_speed
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_init
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initTime
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dual_motor_init
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time_init
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_UnlockPF0
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_GetData
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_I2C
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Init
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureDMA
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[97]"></a>UARTStdioConfig</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, uartstdio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UARTStdioConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConfigureUART
</UL>

<P><STRONG><a name="[82]"></a>UARTwrite</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, uartstdio.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTvprintf
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTgets
</UL>

<P><STRONG><a name="[81]"></a>UARTgets</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, uartstdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTwrite
</UL>

<P><STRONG><a name="[28a]"></a>UARTgetc</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, uartstdio.o(.text), UNUSED)

<P><STRONG><a name="[83]"></a>UARTvprintf</STRONG> (Thumb, 574 bytes, Stack size 64 bytes, uartstdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTwrite
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTprintf
</UL>

<P><STRONG><a name="[84]"></a>UARTprintf</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, uartstdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTvprintf
</UL>

<P><STRONG><a name="[b]"></a>fputc</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, uart_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = fputc
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTCharPut
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[86]"></a>fgetc</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, uart_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTCharGet
</UL>

<P><STRONG><a name="[88]"></a>UART0_IRQHandler</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, uart_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = UART0_IRQHandler &rArr; uDMAChannelTransferSet
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelModeGet
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelTransferSet
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntClear
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uart_app.o(.text)
</UL>
<P><STRONG><a name="[8d]"></a>Uart_Proc</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, uart_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[70]"></a>ConfigureDMA</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, uart_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = ConfigureDMA &rArr; uDMAChannelTransferSet
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralReady
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelTransferSet
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelControlSet
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelAttributeDisable
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelAttributeEnable
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAControlBaseSet
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelEnable
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAEnable
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6f]"></a>ConfigureUART</STRONG> (Thumb, 150 bytes, Stack size 8 bytes, uart_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = ConfigureUART &rArr; UARTIntRegister &rArr; IntRegister
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntPrioritySet
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTStdioConfig
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTClockSourceSet
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntEnable
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntRegister
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTFIFOEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9c]"></a>TIMER0A_Handler</STRONG> (Thumb, 342 bytes, Stack size 40 bytes, uart_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = TIMER0A_Handler &rArr; MPU6050_Read_Data &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_task
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_update
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Read_Data
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntClear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uart_app.o(.text)
</UL>
<P><STRONG><a name="[77]"></a>Time_init</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, uart_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = Time_init &rArr; TimerIntRegister &rArr; _TimerIntNumberGet
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntPrioritySet
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntMasterEnable
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntEnable
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntRegister
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerLoadSet
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerConfigure
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerEnable
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlClockGet
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7e]"></a>scheduler_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, scheduler.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[80]"></a>scheduler_run</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, scheduler.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = scheduler_run
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[74]"></a>Key_Init</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, key_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Key_Init &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIODirModeSet
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a7]"></a>Key_Read</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, key_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Key_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinRead
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
</UL>

<P><STRONG><a name="[1]"></a>Key_Proc</STRONG> (Thumb, 116 bytes, Stack size 8 bytes, key_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Key_Proc &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_start
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Read
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[0]"></a>Adc_Proc</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, adc_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Adc_Proc
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_GetData
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[ab]"></a>ADC_Timer_Init</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, adc_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ADC_Timer_Init &rArr; SysCtlClockGet
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralReady
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerLoadSet
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerControlTrigger
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerConfigure
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerEnable
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlClockGet
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMA_Init
</UL>

<P><STRONG><a name="[ad]"></a>ADC0_Handler</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, adc_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = ADC0_Handler &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntClear
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelModeGet
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelTransferSet
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Address Reference Count : 1]<UL><LI> adc_app.o(.text)
</UL>
<P><STRONG><a name="[73]"></a>ADC_DMA_Init</STRONG> (Thumb, 300 bytes, Stack size 8 bytes, adc_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = ADC_DMA_Init &rArr; ADCIntRegister &rArr; IntRegister
</UL>
<BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeADC
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralReady
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCSequenceDMAEnable
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCHardwareOversampleConfigure
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCSequenceStepConfigure
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCSequenceConfigure
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCSequenceEnable
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntEnable
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntRegister
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelTransferSet
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelControlSet
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelAttributeDisable
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAControlBaseSet
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAChannelEnable
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uDMAEnable
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Timer_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[75]"></a>Init_I2C</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Init_I2C &rArr; GPIOPinTypeI2CSCL &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterInitExpClk
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinConfigure
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2CSCL
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2C
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDelay
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ba]"></a>i2cWriteData</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = i2cWriteData
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataPut
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterControl
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterBusy
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterSlaveAddrSet
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWrite
</UL>

<P><STRONG><a name="[bf]"></a>i2cRead</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = i2cRead
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataGet
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataPut
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterControl
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterBusy
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterSlaveAddrSet
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Double_ReadI2C
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_ReadI2C
</UL>

<P><STRONG><a name="[c1]"></a>i2cWrite</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = i2cWrite &rArr; i2cWriteData
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Init
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_WriteI2C
</UL>

<P><STRONG><a name="[c2]"></a>i2cReadData</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = i2cReadData
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataGet
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataPut
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterControl
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterBusy
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterSlaveAddrSet
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Read_Data
</UL>

<P><STRONG><a name="[c3]"></a>Single_WriteI2C</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, myiic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWrite
</UL>

<P><STRONG><a name="[c4]"></a>Single_ReadI2C</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Single_ReadI2C &rArr; i2cRead
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Init
</UL>

<P><STRONG><a name="[c5]"></a>Double_ReadI2C</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, myiic.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Double_ReadI2C &rArr; i2cRead
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cRead
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_Calibration
</UL>

<P><STRONG><a name="[c6]"></a>Init_I2C0</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, myiic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterInitExpClk
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinConfigure
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2CSCL
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeI2C
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDelay
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
</UL>

<P><STRONG><a name="[c7]"></a>i2c0WriteData</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, myiic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataPut
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterControl
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterBusy
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterSlaveAddrSet
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0Write
</UL>

<P><STRONG><a name="[c8]"></a>i2c0Read</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, myiic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataGet
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataPut
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterControl
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterBusy
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterSlaveAddrSet
</UL>

<P><STRONG><a name="[c9]"></a>i2c0Write</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, myiic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c0WriteData
</UL>

<P><STRONG><a name="[ca]"></a>i2c0ReadData</STRONG> (Thumb, 256 bytes, Stack size 32 bytes, myiic.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataGet
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterDataPut
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterControl
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterBusy
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CMasterSlaveAddrSet
</UL>

<P><STRONG><a name="[cb]"></a>IMU_Calibration</STRONG> (Thumb, 154 bytes, Stack size 8 bytes, icm20608.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = IMU_Calibration &rArr; Double_ReadI2C &rArr; i2cRead
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Double_ReadI2C
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Init
</UL>

<P><STRONG><a name="[76]"></a>ICM20608_Init</STRONG> (Thumb, 166 bytes, Stack size 8 bytes, icm20608.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ICM20608_Init &rArr; IMU_Calibration &rArr; Double_ReadI2C &rArr; i2cRead
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_Calibration
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Single_ReadI2C
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9d]"></a>MPU6050_Read_Data</STRONG> (Thumb, 284 bytes, Stack size 64 bytes, icm20608.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = MPU6050_Read_Data &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2cReadData
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0A_Handler
</UL>

<P><STRONG><a name="[6e]"></a>initTime</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, systicktime.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = initTime &rArr; SysTickIntRegister &rArr; IntRegister
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickPeriodSet
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickIntEnable
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickIntRegister
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTickEnable
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlClockGet
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d1]"></a>micros</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, systicktime.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_Period
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delayMicroseconds
</UL>

<P><STRONG><a name="[d0]"></a>delayMicroseconds</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, systicktime.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = delayMicroseconds
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;micros
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Us
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay
</UL>

<P><STRONG><a name="[d2]"></a>delay</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, systicktime.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay &rArr; delayMicroseconds
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delayMicroseconds
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>

<P><STRONG><a name="[28b]"></a>millis</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, systicktime.o(.text), UNUSED)

<P><STRONG><a name="[d3]"></a>Delay_Ms</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, systicktime.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Delay_Ms &rArr; delay &rArr; delayMicroseconds
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init_Fast
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLEDInit
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Draw_Logo
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>

<P><STRONG><a name="[cc]"></a>delay_ms</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, systicktime.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = delay_ms &rArr; Delay_Ms &rArr; delay &rArr; delayMicroseconds
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ICM20608_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IMU_Calibration
</UL>

<P><STRONG><a name="[d4]"></a>delay_us</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, systicktime.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delayMicroseconds
</UL>

<P><STRONG><a name="[d5]"></a>Delay_Us</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, systicktime.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delayMicroseconds
</UL>

<P><STRONG><a name="[d6]"></a>Test_Period</STRONG> (Thumb, 68 bytes, Stack size 4 bytes, systicktime.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;micros
</UL>

<P><STRONG><a name="[de]"></a>calculate_adaptive_alpha</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, imu.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
</UL>

<P><STRONG><a name="[d8]"></a>invSqrt</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, imu.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_update
</UL>

<P><STRONG><a name="[28c]"></a>kalman_filter</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, imu.o(.text), UNUSED)

<P><STRONG><a name="[28d]"></a>imu_init</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, imu.o(.text), UNUSED)

<P><STRONG><a name="[9e]"></a>imu_update</STRONG> (Thumb, 1410 bytes, Stack size 112 bytes, imu.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = imu_update &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;invSqrt
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;compute_rotation_matrix
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0A_Handler
</UL>

<P><STRONG><a name="[df]"></a>ekf_update</STRONG> (Thumb, 348 bytes, Stack size 24 bytes, imu.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ekf_update
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
</UL>

<P><STRONG><a name="[28e]"></a>is_yaw_outlier</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, imu.o(.text), UNUSED)

<P><STRONG><a name="[9f]"></a>imu_get_euler_angles</STRONG> (Thumb, 430 bytes, Stack size 80 bytes, imu.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = imu_get_euler_angles &rArr; __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ekf_update
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_adaptive_alpha
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0A_Handler
</UL>

<P><STRONG><a name="[e0]"></a>OLED_GPIO_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOOutput
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>

<P><STRONG><a name="[e1]"></a>SDA_OUT</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_IIC_Byte
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_NAck
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Ack
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_IIC_Stop
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_IIC_Start
</UL>

<P><STRONG><a name="[e2]"></a>SDA_IN</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOInput
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
</UL>

<P><STRONG><a name="[e3]"></a>OLED_IIC_Start</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDA_OUT
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>

<P><STRONG><a name="[e4]"></a>OLED_IIC_Stop</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDA_OUT
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
</UL>

<P><STRONG><a name="[e5]"></a>IIC_Wait_Ack</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinRead
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_IIC_Stop
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDA_IN
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_IIC_Byte
</UL>

<P><STRONG><a name="[e6]"></a>IIC_Ack</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDA_OUT
</UL>

<P><STRONG><a name="[e7]"></a>IIC_NAck</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDA_OUT
</UL>

<P><STRONG><a name="[e8]"></a>Write_IIC_Byte</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDA_OUT
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>

<P><STRONG><a name="[e9]"></a>OLED_WrDat</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_IIC_Byte
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_IIC_Stop
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_IIC_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_CLS
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Fill
</UL>

<P><STRONG><a name="[ea]"></a>OLED_WrCmd</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_IIC_Byte
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_IIC_Stop
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_IIC_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init_I2C
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_CLS
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Fill
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
</UL>

<P><STRONG><a name="[eb]"></a>OLED_Set_Pos</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init_I2C
</UL>

<P><STRONG><a name="[ec]"></a>OLED_Fill</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init_I2C
</UL>

<P><STRONG><a name="[ed]"></a>OLED_CLS</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>

<P><STRONG><a name="[ee]"></a>OLED_Init_I2C</STRONG> (Thumb, 186 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Fill
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
</UL>

<P><STRONG><a name="[ef]"></a>LCD_WrDat</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LCD_WrDat
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_oled
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_data
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Draw_Logo
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_clear_L
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P8x16Char
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P8x16Str
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Char
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Str
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CLS
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill
</UL>

<P><STRONG><a name="[f0]"></a>LCD_WrCmd</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LCD_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLEDInit
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_clear_L
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CLS
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Set_Pos
</UL>

<P><STRONG><a name="[f1]"></a>LCD_Set_Pos</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LCD_Set_Pos &rArr; LCD_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_oled
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLEDInit
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Draw_Logo
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_clear_L
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_16_16_CN
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P8x16Char
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P8x16Str
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Char
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Str
</UL>

<P><STRONG><a name="[f2]"></a>LCD_Fill</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrCmd
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLEDInit
</UL>

<P><STRONG><a name="[f3]"></a>LCD_CLS</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LCD_CLS &rArr; LCD_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrCmd
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Draw_Logo
</UL>

<P><STRONG><a name="[f4]"></a>LCD_P6x8Str</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Set_Pos
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_6_8_string
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[f5]"></a>LCD_P6x8Char</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Set_Pos
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[f6]"></a>write_6_8_number</STRONG> (Thumb, 696 bytes, Stack size 48 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Char
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Str
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_6_8_number
</UL>

<P><STRONG><a name="[f7]"></a>LCD_P8x16Str</STRONG> (Thumb, 130 bytes, Stack size 32 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Set_Pos
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_8_16_number
</UL>

<P><STRONG><a name="[f8]"></a>LCD_P8x16Char</STRONG> (Thumb, 154 bytes, Stack size 32 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Set_Pos
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_8_16_number
</UL>

<P><STRONG><a name="[f9]"></a>write_8_16_number</STRONG> (Thumb, 378 bytes, Stack size 48 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P8x16Char
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P8x16Str
</UL>

<P><STRONG><a name="[fa]"></a>write_16_16_CN</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Set_Pos
</UL>

<P><STRONG><a name="[fb]"></a>LCD_clear_L</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Set_Pos
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrCmd
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrDat
</UL>

<P><STRONG><a name="[fc]"></a>Draw_Logo</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Draw_Logo &rArr; LCD_CLS &rArr; LCD_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CLS
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Set_Pos
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrDat
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[fd]"></a>OLEDInit</STRONG> (Thumb, 216 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Fill
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Set_Pos
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrCmd
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>

<P><STRONG><a name="[78]"></a>OLED_Init</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = OLED_Init &rArr; Draw_Logo &rArr; LCD_CLS &rArr; LCD_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOOutput
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_begin
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Draw_Logo
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CLS
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ff]"></a>OLED_Init_Fast</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeGPIOOutput
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinWrite
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_begin
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
</UL>

<P><STRONG><a name="[100]"></a>display_6_8_number</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[101]"></a>display_6_8_string</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Str
</UL>

<P><STRONG><a name="[104]"></a>ssd1306_width</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_pixel
</UL>

<P><STRONG><a name="[105]"></a>ssd1306_height</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_pixel
</UL>

<P><STRONG><a name="[28f]"></a>set_rotation</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, ssd1306.o(.text), UNUSED)

<P><STRONG><a name="[102]"></a>ssd1306_command</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ssd1306.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ssd1306_command &rArr; LCD_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_display
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_dim
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_stop_scroll
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_start_scroll_diag_left
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_start_scroll_diag_right
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_start_scroll_left
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_start_scroll_right
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_invert_display
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_begin
</UL>

<P><STRONG><a name="[fe]"></a>ssd1306_begin</STRONG> (Thumb, 294 bytes, Stack size 8 bytes, ssd1306.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ssd1306_begin &rArr; ssd1306_command &rArr; LCD_WrCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init_Fast
</UL>

<P><STRONG><a name="[103]"></a>ssd1306_draw_pixel</STRONG> (Thumb, 268 bytes, Stack size 16 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_height
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_width
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_char
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_xbitmap
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_bitmap_bg
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_bitmap
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_line
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_circle_helper
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_circle
</UL>

<P><STRONG><a name="[106]"></a>ssd1306_invert_display</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
</UL>

<P><STRONG><a name="[107]"></a>ssd1306_start_scroll_right</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
</UL>

<P><STRONG><a name="[108]"></a>ssd1306_start_scroll_left</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
</UL>

<P><STRONG><a name="[109]"></a>ssd1306_start_scroll_diag_right</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
</UL>

<P><STRONG><a name="[10a]"></a>ssd1306_start_scroll_diag_left</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
</UL>

<P><STRONG><a name="[10b]"></a>ssd1306_stop_scroll</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
</UL>

<P><STRONG><a name="[10c]"></a>ssd1306_dim</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
</UL>

<P><STRONG><a name="[10d]"></a>ssd1306_data</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrDat
</UL>

<P><STRONG><a name="[10e]"></a>draw_oled</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Set_Pos
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_display
</UL>

<P><STRONG><a name="[10f]"></a>ssd1306_display</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_oled
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Oled_Proc
</UL>

<P><STRONG><a name="[110]"></a>ssd1306_clear_display</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, ssd1306.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ssd1306_clear_display
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Oled_Proc
</UL>

<P><STRONG><a name="[114]"></a>ssd1306_draw_fast_hline_internal</STRONG> (Thumb, 174 bytes, Stack size 20 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_vline
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_hline
</UL>

<P><STRONG><a name="[113]"></a>ssd1306_draw_fast_vline_internal</STRONG> (Thumb, 378 bytes, Stack size 28 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_vline
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_hline
</UL>

<P><STRONG><a name="[112]"></a>ssd1306_draw_fast_hline</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_vline_internal
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_hline_internal
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_fill_triangle
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_round_rect
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_rect
</UL>

<P><STRONG><a name="[115]"></a>ssd1306_draw_fast_vline</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_vline_internal
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_hline_internal
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_round_rect
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_fill_rect
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_rect
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_fill_circle
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_fill_circle_helper
</UL>

<P><STRONG><a name="[116]"></a>ssd1306_draw_circle</STRONG> (Thumb, 286 bytes, Stack size 52 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_pixel
</UL>

<P><STRONG><a name="[117]"></a>ssd1306_draw_circle_helper</STRONG> (Thumb, 230 bytes, Stack size 40 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_pixel
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_round_rect
</UL>

<P><STRONG><a name="[118]"></a>ssd1306_fill_circle_helper</STRONG> (Thumb, 198 bytes, Stack size 44 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_vline
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_fill_round_rect
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_fill_circle
</UL>

<P><STRONG><a name="[119]"></a>ssd1306_fill_circle</STRONG> (Thumb, 48 bytes, Stack size 28 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_fill_circle_helper
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_vline
</UL>

<P><STRONG><a name="[11a]"></a>ssd1306_draw_line</STRONG> (Thumb, 206 bytes, Stack size 40 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_pixel
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_triangle
</UL>

<P><STRONG><a name="[11b]"></a>ssd1306_draw_rect</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_vline
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_hline
</UL>

<P><STRONG><a name="[11c]"></a>ssd1306_fill_rect</STRONG> (Thumb, 46 bytes, Stack size 28 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_vline
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_char
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_fill_round_rect
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_fill_screen
</UL>

<P><STRONG><a name="[11d]"></a>ssd1306_fill_screen</STRONG> (Thumb, 28 bytes, Stack size 12 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_fill_rect
</UL>

<P><STRONG><a name="[11e]"></a>ssd1306_draw_round_rect</STRONG> (Thumb, 210 bytes, Stack size 32 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_circle_helper
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_vline
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_hline
</UL>

<P><STRONG><a name="[11f]"></a>ssd1306_fill_round_rect</STRONG> (Thumb, 108 bytes, Stack size 36 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_fill_rect
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_fill_circle_helper
</UL>

<P><STRONG><a name="[120]"></a>ssd1306_draw_triangle</STRONG> (Thumb, 64 bytes, Stack size 36 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_line
</UL>

<P><STRONG><a name="[121]"></a>ssd1306_fill_triangle</STRONG> (Thumb, 400 bytes, Stack size 72 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_fast_hline
</UL>

<P><STRONG><a name="[122]"></a>ssd1306_draw_bitmap</STRONG> (Thumb, 100 bytes, Stack size 36 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_pixel
</UL>

<P><STRONG><a name="[123]"></a>ssd1306_draw_bitmap_bg</STRONG> (Thumb, 126 bytes, Stack size 40 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_pixel
</UL>

<P><STRONG><a name="[124]"></a>ssd1306_draw_xbitmap</STRONG> (Thumb, 110 bytes, Stack size 36 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_pixel
</UL>

<P><STRONG><a name="[125]"></a>ssd1306_draw_char</STRONG> (Thumb, 250 bytes, Stack size 40 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_fill_rect
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_pixel
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_write
</UL>

<P><STRONG><a name="[126]"></a>ssd1306_write</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_draw_char
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_puts
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_putstring
</UL>

<P><STRONG><a name="[12d]"></a>ssd1306_set_cursor</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayStringLine
</UL>

<P><STRONG><a name="[290]"></a>ssd1306_get_cursor_x</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ssd1306.o(.text), UNUSED)

<P><STRONG><a name="[291]"></a>ssd1306_get_cursor_y</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ssd1306.o(.text), UNUSED)

<P><STRONG><a name="[12a]"></a>ssd1306_set_textsize</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ssd1306.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayStringLine
</UL>

<P><STRONG><a name="[12b]"></a>ssd1306_set_textcolor</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ssd1306.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayStringLine
</UL>

<P><STRONG><a name="[292]"></a>ssd1306_set_textcolor_bg</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ssd1306.o(.text), UNUSED)

<P><STRONG><a name="[293]"></a>ssd1306_set_textwrap</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, ssd1306.o(.text), UNUSED)

<P><STRONG><a name="[294]"></a>ssd1306_get_rotation</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, ssd1306.o(.text), UNUSED)

<P><STRONG><a name="[129]"></a>ssd1306_set_rotation</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, ssd1306.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init
</UL>

<P><STRONG><a name="[295]"></a>ssd1306_cp437</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, ssd1306.o(.text), UNUSED)

<P><STRONG><a name="[127]"></a>ssd1306_putstring</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_write
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_puts
</UL>

<P><STRONG><a name="[128]"></a>ssd1306_puts</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, ssd1306.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_putstring
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_write
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayStringLine
</UL>

<P><STRONG><a name="[79]"></a>Init</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, oled_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Init &rArr; ssd1306_clear_display
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_rotation
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_textcolor
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_textsize
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_clear_display
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12c]"></a>LCD_DisplayStringLine</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, oled_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_puts
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_textcolor
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_textsize
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_set_cursor
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LcdSprintf
</UL>

<P><STRONG><a name="[12e]"></a>LcdSprintf</STRONG> (Thumb, 42 bytes, Stack size 56 bytes, oled_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_DisplayStringLine
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Oled_Proc
</UL>

<P><STRONG><a name="[130]"></a>Oled_Proc</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, oled_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_GetData
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LcdSprintf
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_clear_display
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_display
</UL>

<P><STRONG><a name="[71]"></a>QEIApp_UnlockPF0</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, qei_app.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[72]"></a>QEIApp_Init</STRONG> (Thumb, 392 bytes, Stack size 8 bytes, qei_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = QEIApp_Init &rArr; GPIOPinTypeQEI &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntPrioritySet
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinConfigure
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypeQEI
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPadConfigSet
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralReady
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntEnable
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlClockGet
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIIntEnable
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIIntRegister
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIVelocityConfigure
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIVelocityDisable
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIVelocityEnable
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIPositionSet
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIConfigure
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIDisable
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIEnable
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[296]"></a>QEIApp_GetLeftSignedVelocity</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, qei_app.o(.text), UNUSED)

<P><STRONG><a name="[297]"></a>QEIApp_GetRightSignedVelocity</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, qei_app.o(.text), UNUSED)

<P><STRONG><a name="[298]"></a>QEIApp_GetLeftRPM</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, qei_app.o(.text), UNUSED)

<P><STRONG><a name="[299]"></a>QEIApp_GetRightRPM</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, qei_app.o(.text), UNUSED)

<P><STRONG><a name="[7f]"></a>QEIApp_GetData</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, qei_app.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adc_Proc
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_update_speed
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Oled_Proc
</UL>

<P><STRONG><a name="[143]"></a>PWM_APP_GetChannelConfig</STRONG> (Thumb, 192 bytes, Stack size 20 bytes, pwm_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = PWM_APP_GetChannelConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_get_level
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_get_duty
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_SetServo
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_SetDuty
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_SetPulseWidth
</UL>

<P><STRONG><a name="[13c]"></a>PWM_APP_Init</STRONG> (Thumb, 568 bytes, Stack size 16 bytes, pwm_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = PWM_APP_Init &rArr; GPIOPinTypePWM &rArr; GPIOPadConfigSet
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinConfigure
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOPinTypePWM
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDelay
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPWMClockSet
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlPeripheralEnable
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMOutputState
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMGenEnable
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMGenPeriodSet
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMGenConfigure
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dual_motor_init
</UL>

<P><STRONG><a name="[142]"></a>PWM_APP_SetPulseWidth</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, pwm_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMPulseWidthSet
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_GetChannelConfig
</UL>

<P><STRONG><a name="[145]"></a>PWM_APP_SetDuty</STRONG> (Thumb, 64 bytes, Stack size 32 bytes, pwm_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = PWM_APP_SetDuty &rArr; PWM_APP_GetChannelConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMPulseWidthSet
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMGenPeriodGet
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_GetChannelConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dual_motor_init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_left_motor_set_speed
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_right_motor_set_speed
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;right_motor_set_speed
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;left_motor_set_speed
</UL>

<P><STRONG><a name="[147]"></a>PWM_APP_SetServo</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, pwm_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMPulseWidthSet
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_GetChannelConfig
</UL>

<P><STRONG><a name="[148]"></a>PWM_APP_OutputMulti</STRONG> (Thumb, 196 bytes, Stack size 24 bytes, pwm_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMPulseWidthSet
</UL>

<P><STRONG><a name="[7b]"></a>pwm_debug_init</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, pwm_debug.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14a]"></a>pwm_debug_get_duty</STRONG> (Thumb, 60 bytes, Stack size 32 bytes, pwm_debug.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMPulseWidthGet
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMGenPeriodGet
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_GetChannelConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_report_multi
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_report
</UL>

<P><STRONG><a name="[14c]"></a>pwm_debug_get_level</STRONG> (Thumb, 134 bytes, Stack size 48 bytes, pwm_debug.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_gen_for_channel
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_GetChannelConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_report_multi
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_report
</UL>

<P><STRONG><a name="[14d]"></a>pwm_debug_report</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, pwm_debug.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_get_level
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_get_duty
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[6d]"></a>pwm_debug_report_multi</STRONG> (Thumb, 276 bytes, Stack size 96 bytes, pwm_debug.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_get_level
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_get_duty
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_proc
</UL>

<P><STRONG><a name="[7a]"></a>dual_motor_init</STRONG> (Thumb, 78 bytes, Stack size 24 bytes, motor_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = dual_motor_init &rArr; PWM_APP_SetDuty &rArr; PWM_APP_GetChannelConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_SetDuty
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14f]"></a>left_motor_set_speed</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, motor_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_SetDuty
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_stop
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_spin_right
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_spin_left
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_turn_right
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_turn_left
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_backward
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_forward
</UL>

<P><STRONG><a name="[150]"></a>right_motor_set_speed</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, motor_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_SetDuty
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_stop
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_spin_right
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_spin_left
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_turn_right
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_turn_left
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_backward
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_forward
</UL>

<P><STRONG><a name="[151]"></a>my_right_motor_set_speed</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, motor_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = my_right_motor_set_speed &rArr; PWM_APP_SetDuty &rArr; PWM_APP_GetChannelConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_SetDuty
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_calc
</UL>

<P><STRONG><a name="[152]"></a>my_left_motor_set_speed</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, motor_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = my_left_motor_set_speed &rArr; PWM_APP_SetDuty &rArr; PWM_APP_GetChannelConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_APP_SetDuty
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_calc
</UL>

<P><STRONG><a name="[153]"></a>car_forward</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, motor_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;right_motor_set_speed
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;left_motor_set_speed
</UL>

<P><STRONG><a name="[154]"></a>car_backward</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, motor_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;right_motor_set_speed
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;left_motor_set_speed
</UL>

<P><STRONG><a name="[155]"></a>car_turn_left</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, motor_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;right_motor_set_speed
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;left_motor_set_speed
</UL>

<P><STRONG><a name="[156]"></a>car_turn_right</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, motor_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;right_motor_set_speed
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;left_motor_set_speed
</UL>

<P><STRONG><a name="[157]"></a>car_spin_left</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, motor_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;right_motor_set_speed
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;left_motor_set_speed
</UL>

<P><STRONG><a name="[158]"></a>car_spin_right</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, motor_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;right_motor_set_speed
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;left_motor_set_speed
</UL>

<P><STRONG><a name="[159]"></a>car_stop</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, motor_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;right_motor_set_speed
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;left_motor_set_speed
</UL>
<BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_stop
</UL>

<P><STRONG><a name="[15f]"></a>pid_init</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, pid.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_init
</UL>

<P><STRONG><a name="[160]"></a>pid_set_target</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, pid.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_set_target_speed
</UL>

<P><STRONG><a name="[168]"></a>pid_set_params</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_set_right_params
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_set_left_params
</UL>

<P><STRONG><a name="[169]"></a>pid_set_limit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_set_right_params
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_set_left_params
</UL>

<P><STRONG><a name="[164]"></a>pid_reset</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, pid.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_start
</UL>

<P><STRONG><a name="[15a]"></a>pid_calculate_positional</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, pid.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_formula_positional
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_out_limit
</UL>

<P><STRONG><a name="[15d]"></a>pid_calculate_incremental</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, pid.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = pid_calculate_incremental
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_formula_incremental
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_out_limit
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_calc
</UL>

<P><STRONG><a name="[7c]"></a>pid_app_init</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, pid_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = pid_app_init
</UL>
<BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_init
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7d]"></a>pid_app_set_target_speed</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, pid_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = pid_app_set_target_speed
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_set_target
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[161]"></a>pid_app_update_speed</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, pid_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = pid_app_update_speed
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_GetData
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_calc
</UL>

<P><STRONG><a name="[162]"></a>pid_app_calc</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, pid_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = pid_app_calc &rArr; my_left_motor_set_speed &rArr; PWM_APP_SetDuty &rArr; PWM_APP_GetChannelConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_update_speed
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calculate_incremental
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_left_motor_set_speed
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_right_motor_set_speed
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_task
</UL>

<P><STRONG><a name="[a0]"></a>pid_app_task</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, pid_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = pid_app_task &rArr; pid_app_calc &rArr; my_left_motor_set_speed &rArr; PWM_APP_SetDuty &rArr; PWM_APP_GetChannelConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_calc
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0A_Handler
</UL>

<P><STRONG><a name="[aa]"></a>pid_app_start</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, pid_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = pid_app_start
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
</UL>

<P><STRONG><a name="[165]"></a>pid_app_stop</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, pid_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;car_stop
</UL>

<P><STRONG><a name="[166]"></a>pid_app_set_left_params</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, pid_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_set_limit
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_set_params
</UL>

<P><STRONG><a name="[16a]"></a>pid_app_set_right_params</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, pid_app.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_set_limit
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_set_params
</UL>

<P><STRONG><a name="[29a]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[167]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_set_right_params
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_set_left_params
</UL>

<P><STRONG><a name="[29b]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[16b]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[29c]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[29d]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[111]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_clear_display
</UL>

<P><STRONG><a name="[8e]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_Proc
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_report_multi
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIApp_Init
</UL>

<P><STRONG><a name="[29e]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[16c]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[b1]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Read_Data
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[171]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[172]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[b0]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[af]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Read_Data
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
</UL>

<P><STRONG><a name="[b2]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_Read_Data
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Handler
</UL>

<P><STRONG><a name="[29f]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[189]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[174]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[16d]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[2a0]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[16e]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[2a1]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[2a2]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[173]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[2a3]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[170]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>

<P><STRONG><a name="[16f]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[176]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[177]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[186]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[10]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[2a4]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[175]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[2a5]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[2a6]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[2a7]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[179]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2a8]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[6b]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_Proc
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_report_multi
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_send
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_report
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_gen_for_channel
</UL>

<P><STRONG><a name="[2a9]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[2aa]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[17b]"></a>__0snprintf</STRONG> (Thumb, 48 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2ab]"></a>__1snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[14e]"></a>__2snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_report_multi
</UL>

<P><STRONG><a name="[2ac]"></a>__c89snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[2ad]"></a>snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[17c]"></a>__0vsprintf</STRONG> (Thumb, 30 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2ae]"></a>__1vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[2af]"></a>__2vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[2b0]"></a>__c89vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[12f]"></a>vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LcdSprintf
</UL>

<P><STRONG><a name="[17e]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[db]"></a>__hardfp_asinf</STRONG> (Thumb, 258 bytes, Stack size 16 bytes, asinf.o(i.__hardfp_asinf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __hardfp_asinf &rArr; sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
</UL>

<P><STRONG><a name="[da]"></a>__hardfp_atan2f</STRONG> (Thumb, 594 bytes, Stack size 32 bytes, atan2f.o(i.__hardfp_atan2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __hardfp_atan2f
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan2
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
</UL>

<P><STRONG><a name="[dc]"></a>__hardfp_cosf</STRONG> (Thumb, 280 bytes, Stack size 8 bytes, cosf.o(i.__hardfp_cosf))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = __hardfp_cosf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
</UL>

<P><STRONG><a name="[dd]"></a>__hardfp_sinf</STRONG> (Thumb, 344 bytes, Stack size 16 bytes, sinf.o(i.__hardfp_sinf))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_get_euler_angles
</UL>

<P><STRONG><a name="[d7]"></a>__hardfp_sqrtf</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, sqrtf.o(i.__hardfp_sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_update
</UL>

<P><STRONG><a name="[180]"></a>__mathlib_flt_infnan</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[183]"></a>__mathlib_flt_infnan2</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan2))
<BR><BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[182]"></a>__mathlib_flt_invalid</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_invalid))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[17f]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[184]"></a>__mathlib_rredf2</STRONG> (Thumb, 316 bytes, Stack size 20 bytes, rredf.o(i.__mathlib_rredf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __mathlib_rredf2
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[2b1]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[2b2]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[2b3]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[181]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[17d]"></a>sqrtf</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, sqrtf.o(i.sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[13]"></a>_ADCIntNumberGet</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _ADCIntNumberGet
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntUnregister
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADCIntRegister
</UL>

<P><STRONG><a name="[1a]"></a>_GPIOIntNumberGet</STRONG> (Thumb, 56 bytes, Stack size 12 bytes, gpio.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOIntUnregister
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOIntRegister
</UL>

<P><STRONG><a name="[40]"></a>_I2CIntNumberGet</STRONG> (Thumb, 56 bytes, Stack size 12 bytes, i2c.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CIntUnregister
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2CIntRegister
</UL>

<P><STRONG><a name="[7]"></a>_IntDefaultHandler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, interrupt.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> interrupt.o(.text)
</UL>
<P><STRONG><a name="[4b]"></a>_PWMGenIntNumberGet</STRONG> (Thumb, 248 bytes, Stack size 8 bytes, pwm.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMGenIntUnregister
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMGenIntRegister
</UL>

<P><STRONG><a name="[4e]"></a>_PWMFaultIntNumberGet</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, pwm.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMFaultIntUnregister
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWMFaultIntRegister
</UL>

<P><STRONG><a name="[51]"></a>_QEIIntNumberGet</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, qei.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIIntUnregister
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIIntRegister
</UL>

<P><STRONG><a name="[59]"></a>_SysCtlMemTimingGet</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlDeepSleepClockConfigSet
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlClockFreqSet
</UL>

<P><STRONG><a name="[5a]"></a>_SysCtlFrequencyGet</STRONG> (Thumb, 96 bytes, Stack size 20 bytes, sysctl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtlClockFreqSet
</UL>

<P><STRONG><a name="[60]"></a>_TimerIntNumberGet</STRONG> (Thumb, 68 bytes, Stack size 20 bytes, timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = _TimerIntNumberGet
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntUnregister
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimerIntRegister
</UL>

<P><STRONG><a name="[66]"></a>_UARTIntNumberGet</STRONG> (Thumb, 56 bytes, Stack size 12 bytes, uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _UARTIntNumberGet
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntUnregister
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTIntRegister
</UL>

<P><STRONG><a name="[3]"></a>NmiSR</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_rvmdk.o(RESET))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NmiSR
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NmiSR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_rvmdk.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>FaultISR</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_rvmdk.o(RESET))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FaultISR
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FaultISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_rvmdk.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>IntDefaultHandler</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_rvmdk.o(RESET))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDefaultHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntDefaultHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_rvmdk.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>SycTickHandler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, systicktime.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> systicktime.o(.text)
</UL>
<P><STRONG><a name="[d9]"></a>compute_rotation_matrix</STRONG> (Thumb, 358 bytes, Stack size 0 bytes, imu.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_update
</UL>

<P><STRONG><a name="[a]"></a>QEIApp_QEI0IntHandler</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, qei_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = QEIApp_QEI0IntHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIIntClear
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIVelocityGet
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIDirectionGet
</UL>
<BR>[Address Reference Count : 1]<UL><LI> qei_app.o(.text)
</UL>
<P><STRONG><a name="[9]"></a>QEIApp_QEI1IntHandler</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, qei_app.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = QEIApp_QEI1IntHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIIntClear
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIVelocityGet
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEIDirectionGet
</UL>
<BR>[Address Reference Count : 1]<UL><LI> qei_app.o(.text)
</UL>
<P><STRONG><a name="[149]"></a>get_gen_for_channel</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, pwm_debug.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pwm_debug_get_level
</UL>

<P><STRONG><a name="[15c]"></a>pid_out_limit</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, pid.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calculate_incremental
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calculate_positional
</UL>

<P><STRONG><a name="[15b]"></a>pid_formula_positional</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calculate_positional
</UL>

<P><STRONG><a name="[15e]"></a>pid_formula_incremental</STRONG> (Thumb, 142 bytes, Stack size 0 bytes, pid.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calculate_incremental
</UL>

<P><STRONG><a name="[163]"></a>constrain</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, pid_app.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_app_calc
</UL>

<P><STRONG><a name="[185]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[17a]"></a>_printf_core</STRONG> (Thumb, 1704 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsprintf
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0snprintf
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[188]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[187]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[c]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0snprintf)
</UL>
<P><STRONG><a name="[d]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0vsprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
