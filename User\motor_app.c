/**
 * @file motor_app.c
 * @brief 双轮电机控制模块，基于双PWM控制方式
 * <AUTHOR>
 * @copyright 米醋电子工作室
 */

#include "pwm_app.h"
#include "sysctl.h"
#include "gpio.h"
#include "hw_memmap.h"
#include "stdbool.h"
#include "stdint.h"

// 静态变量保存配置
static bool g_is_initialized = false;

// 定义电机的PWM通道
// 右轮
#define RIGHT_MOTOR_PWM1 PWM_APP_CHANNEL_M0_0 // PH0 - M0PWM0
#define RIGHT_MOTOR_PWM2 PWM_APP_CHANNEL_M0_1 // PH1 - M0PWM1
// 左轮
#define LEFT_MOTOR_PWM1 PWM_APP_CHANNEL_M0_2 // PH2 - M0PWM2
#define LEFT_MOTOR_PWM2 PWM_APP_CHANNEL_M0_3 // PH3 - M0PWM3

// 电机方向定义
typedef enum
{
    MOTOR_DIR_FORWARD = 0, // 正向
    MOTOR_DIR_REVERSE = 1, // 反向
    MOTOR_DIR_STOP = 2     // 停止
} motor_direction_t;

/**
 * @brief 初始化双轮电机控制模块
 * @return true 初始化成功，false 初始化失败
 */
bool dual_motor_init(void)
{
    if (g_is_initialized)
    {
        return true; // 已经初始化过
    }

    // 初始化 PWM 模块0，设置 PWM 时钟分频和周期
    pwm_app_config_t pwm_config = {
        .pwm_div = PWM_APP_SYSCTL_PWMDIV_8,    // 80MHz/8=10MHz
        .gen_mode = PWM_APP_GEN_MODE_DOWN,     // 递减计数模式
        .sync_mode = PWM_APP_GEN_MODE_NO_SYNC, // 无同步
        .period = PWM_APP_PERIOD_50000US       // 20ms周期 (50Hz)
    };

    // 初始化 PWM 模块
    if (!PWM_APP_Init(PWM_APP_MODULE_0, &pwm_config))
    {
        return false;
    }

    // 初始状态：停止电机
    PWM_APP_SetDuty(RIGHT_MOTOR_PWM1, 0);
    PWM_APP_SetDuty(RIGHT_MOTOR_PWM2, 0);
    PWM_APP_SetDuty(LEFT_MOTOR_PWM1, 0);
    PWM_APP_SetDuty(LEFT_MOTOR_PWM2, 0);

    g_is_initialized = true;
    return true;
}

/**
 * @brief 设置左轮电机速度和方向
 * @param speed 速度（0-100），越大转速越快
 * @param direction 电机方向
 * @return true 设置成功，false 设置失败
 */
bool left_motor_set_speed(uint8_t speed, motor_direction_t direction)
{
    if (!g_is_initialized)
    {
        return false;
    }

    // 限制速度范围
    if (speed > 100)
        speed = 100;

    bool success = true;

    // 根据方向设置PWM输出
    switch (direction)
    {
    case MOTOR_DIR_FORWARD:
        success &= PWM_APP_SetDuty(LEFT_MOTOR_PWM1, speed);
        success &= PWM_APP_SetDuty(LEFT_MOTOR_PWM2, 0);
        break;

    case MOTOR_DIR_REVERSE:
        success &= PWM_APP_SetDuty(LEFT_MOTOR_PWM1, 0);
        success &= PWM_APP_SetDuty(LEFT_MOTOR_PWM2, speed);
        break;

    case MOTOR_DIR_STOP:
    default:
        success &= PWM_APP_SetDuty(LEFT_MOTOR_PWM1, 0);
        success &= PWM_APP_SetDuty(LEFT_MOTOR_PWM2, 0);
        break;
    }

    return success;
}

/**
 * @brief 设置右轮电机速度和方向
 * @param speed 速度（0-100），越大转速越快
 * @param direction 电机方向
 * @return true 设置成功，false 设置失败
 */
bool right_motor_set_speed(uint8_t speed, motor_direction_t direction)
{
    if (!g_is_initialized)
    {
        return false;
    }
    
    // 限制速度范围
    if (speed > 100)
        speed = 99;
    
    bool success = true;
    
    // 根据方向设置PWM输出
    switch (direction)
    {
        case MOTOR_DIR_FORWARD:
            success &= PWM_APP_SetDuty(RIGHT_MOTOR_PWM1, speed);
            success &= PWM_APP_SetDuty(RIGHT_MOTOR_PWM2, 0);
            break;
            
        case MOTOR_DIR_REVERSE:
            success &= PWM_APP_SetDuty(RIGHT_MOTOR_PWM1, 0);
            success &= PWM_APP_SetDuty(RIGHT_MOTOR_PWM2, speed);
            break;
            
        case MOTOR_DIR_STOP:
        default:
            success &= PWM_APP_SetDuty(RIGHT_MOTOR_PWM1, 0);
            success &= PWM_APP_SetDuty(RIGHT_MOTOR_PWM2, 0);
            break;
    }
    
    return success;
}

bool my_right_motor_set_speed(int16_t speed)
{
    if (!g_is_initialized)
    {
        return false;
    }
    
    // 限制速度范围
    if (speed > 100)
        speed = 99;
    else if (speed < (-100))
        speed = -99;
    
    bool success = true;
    uint8_t abs_speed = speed > 0 ? speed : -speed;
    
    // 根据速度正负设置PWM输出
    if (speed > 0) {
        // 右轮正转（注意方向设置与左轮相反）
        success &= PWM_APP_SetDuty(RIGHT_MOTOR_PWM1, 100 - abs_speed);
        success &= PWM_APP_SetDuty(RIGHT_MOTOR_PWM2, 0);
    } else if (speed < 0) {
        // 右轮反转（注意方向设置与左轮相反）
        success &= PWM_APP_SetDuty(RIGHT_MOTOR_PWM1, 0);
        success &= PWM_APP_SetDuty(RIGHT_MOTOR_PWM2, 100 - abs_speed);
    } else {
        // 停止
        success &= PWM_APP_SetDuty(RIGHT_MOTOR_PWM1, 0);
        success &= PWM_APP_SetDuty(RIGHT_MOTOR_PWM2, 0);
    }
    
    return success;
}

bool my_left_motor_set_speed(int16_t speed)
{
    if (!g_is_initialized)
    {
        return false;
    }

    // 限制速度范围
    if (speed > 100)
        speed = 100;
    else if (speed < (-100))
        speed = -100;

    bool success = true;
    uint8_t abs_speed = speed > 0 ? speed : -speed;

    // 根据速度正负设置PWM输出
    if (speed > 0) {
        // 正转
        success &= PWM_APP_SetDuty(LEFT_MOTOR_PWM1, 0);
        success &= PWM_APP_SetDuty(LEFT_MOTOR_PWM2, 100 - abs_speed);
    } else if (speed < 0) {
        // 反转
        success &= PWM_APP_SetDuty(LEFT_MOTOR_PWM1, 100 - abs_speed);
        success &= PWM_APP_SetDuty(LEFT_MOTOR_PWM2, 0);
    } else {
        // 停止
        success &= PWM_APP_SetDuty(LEFT_MOTOR_PWM1, 0);
        success &= PWM_APP_SetDuty(LEFT_MOTOR_PWM2, 0);
    }

    return success;
}

/**
 * @brief 小车前进
 * @param speed 速度（0-100）
 * @return true 设置成功，false 设置失败
 */
bool car_forward(uint8_t speed)
{
    if (!g_is_initialized)
    {
        return false;
    }

    bool left_ok = left_motor_set_speed(speed, MOTOR_DIR_FORWARD);
    bool right_ok = right_motor_set_speed(speed, MOTOR_DIR_FORWARD);

    return left_ok && right_ok;
}

/**
 * @brief 小车后退
 * @param speed 速度（0-100）
 * @return true 设置成功，false 设置失败
 */
bool car_backward(uint8_t speed)
{
    if (!g_is_initialized)
    {
        return false;
    }

    bool left_ok = left_motor_set_speed(speed, MOTOR_DIR_REVERSE);
    bool right_ok = right_motor_set_speed(speed, MOTOR_DIR_REVERSE);

    return left_ok && right_ok;
}

/**
 * @brief 小车左转
 * @param speed 速度（0-100）
 * @return true 设置成功，false 设置失败
 */
bool car_turn_left(uint8_t speed)
{
    if (!g_is_initialized)
    {
        return false;
    }

    bool left_ok = left_motor_set_speed(speed / 2, MOTOR_DIR_FORWARD);
    bool right_ok = right_motor_set_speed(speed, MOTOR_DIR_FORWARD);

    return left_ok && right_ok;
}

/**
 * @brief 小车右转
 * @param speed 速度（0-100）
 * @return true 设置成功，false 设置失败
 */
bool car_turn_right(uint8_t speed)
{
    if (!g_is_initialized)
    {
        return false;
    }

    bool left_ok = left_motor_set_speed(speed, MOTOR_DIR_FORWARD);
    bool right_ok = right_motor_set_speed(speed / 2, MOTOR_DIR_FORWARD);

    return left_ok && right_ok;
}

/**
 * @brief 小车原地左转
 * @param speed 速度（0-100）
 * @return true 设置成功，false 设置失败
 */
bool car_spin_left(uint8_t speed)
{
    if (!g_is_initialized)
    {
        return false;
    }

    bool left_ok = left_motor_set_speed(speed, MOTOR_DIR_REVERSE);
    bool right_ok = right_motor_set_speed(speed, MOTOR_DIR_FORWARD);

    return left_ok && right_ok;
}

/**
 * @brief 小车原地右转
 * @param speed 速度（0-100）
 * @return true 设置成功，false 设置失败
 */
bool car_spin_right(uint8_t speed)
{
    if (!g_is_initialized)
    {
        return false;
    }

    bool left_ok = left_motor_set_speed(speed, MOTOR_DIR_FORWARD);
    bool right_ok = right_motor_set_speed(speed, MOTOR_DIR_REVERSE);
    return left_ok && right_ok;
}



/**
 * @brief 小车停止
 * @return true 设置成功，false 设置失败
 */
bool car_stop(void)
{
    if (!g_is_initialized)
    {
        return false;
    }

    bool left_ok = left_motor_set_speed(0, MOTOR_DIR_STOP);
    bool right_ok = right_motor_set_speed(0, MOTOR_DIR_STOP);

    return left_ok && right_ok;
}


