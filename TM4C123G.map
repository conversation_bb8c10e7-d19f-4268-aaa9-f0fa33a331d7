Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    adc.o(.text) refers to interrupt.o(.text) for IntRegister
    adc.o(.text) refers to adc.o(.data) for g_pui8OversampleFactor
    aes.o(.text) refers to interrupt.o(.text) for IntRegister
    can.o(.text) refers to interrupt.o(.text) for IntRegister
    can.o(.text) refers to can.o(.constdata) for g_ui16CANBitValues
    comp.o(.text) refers to interrupt.o(.text) for IntRegister
    des.o(.text) refers to interrupt.o(.text) for IntRegister
    eeprom.o(.text) refers to sysctl.o(.emb_text) for SysCtlDelay
    eeprom.o(.text) refers to sysctl.o(.text) for SysCtlPeripheralReset
    emac.o(.text) refers to sysctl.o(.text) for SysCtlPeripheralReset
    emac.o(.text) refers to sysctl.o(.emb_text) for SysCtlDelay
    emac.o(.text) refers to sw_crc.o(.text) for Crc32
    emac.o(.text) refers to interrupt.o(.text) for IntRegister
    emac.o(.text) refers to emac.o(.constdata) for g_pi16MIIClockDiv
    epi.o(.text) refers to interrupt.o(.text) for IntRegister
    flash.o(.text) refers to interrupt.o(.text) for IntRegister
    flash.o(.text) refers to flash.o(.constdata) for g_pui32FMPRERegs
    gpio.o(.text) refers to interrupt.o(.text) for IntRegister
    gpio.o(.text) refers to gpio.o(.constdata) for g_ppui32GPIOIntMapBlizzard
    hibernate.o(.text) refers to interrupt.o(.text) for IntRegister
    i2c.o(.text) refers to interrupt.o(.text) for IntRegister
    i2c.o(.text) refers to i2c.o(.constdata) for g_ppui32I2CIntMap
    interrupt.o(.text) refers to cpu.o(.emb_text) for CPUcpsie
    interrupt.o(.text) refers to interrupt.o(vtable) for g_pfnRAMVectors
    interrupt.o(.text) refers to interrupt.o(.constdata) for g_pui32Priority
    lcd.o(.text) refers to sysctl.o(.emb_text) for SysCtlDelay
    lcd.o(.text) refers to interrupt.o(.text) for IntRegister
    mpu.o(.text) refers to interrupt.o(.text) for IntRegister
    pwm.o(.text) refers to interrupt.o(.text) for IntRegister
    qei.o(.text) refers to interrupt.o(.text) for IntRegister
    shamd5.o(.text) refers to interrupt.o(.text) for IntRegister
    ssi.o(.text) refers to interrupt.o(.text) for IntRegister
    ssi.o(.text) refers to ssi.o(.constdata) for g_ppui32SSIIntMap
    sw_crc.o(.text) refers to sw_crc.o(.constdata) for g_pui8Crc8CCITT
    sysctl.o(.text) refers to interrupt.o(.text) for IntRegister
    sysctl.o(.text) refers to sysctl.o(.constdata) for g_sXTALtoMEMTIM
    sysctl.o(.text) refers to cpu.o(.emb_text) for CPUwfi
    sysctl.o(.text) refers to sysctl.o(.emb_text) for SysCtlDelay
    sysexc.o(.text) refers to interrupt.o(.text) for IntRegister
    systick.o(.text) refers to interrupt.o(.text) for IntRegister
    timer.o(.text) refers to interrupt.o(.text) for IntRegister
    timer.o(.text) refers to timer.o(.constdata) for g_ppui32TimerIntMap
    uart.o(.text) refers to interrupt.o(.text) for IntRegister
    uart.o(.text) refers to uart.o(.constdata) for g_ppui32UARTIntMap
    udma.o(.text) refers to interrupt.o(.text) for IntRegister
    usb.o(.text) refers to interrupt.o(.text) for IntRegister
    watchdog.o(.text) refers to interrupt.o(.text) for IntRegister
    main.o(.text) refers to printfa.o(i.__0printf) for __2printf
    main.o(.text) refers to pwm_debug.o(.text) for pwm_debug_report_multi
    main.o(.text) refers to systicktime.o(.text) for initTime
    main.o(.text) refers to uart_app.o(.text) for ConfigureUART
    main.o(.text) refers to qei_app.o(.text) for QEIApp_UnlockPF0
    main.o(.text) refers to adc_app.o(.text) for ADC_DMA_Init
    main.o(.text) refers to key_app.o(.text) for Key_Init
    main.o(.text) refers to myiic.o(.text) for Init_I2C
    main.o(.text) refers to icm20608.o(.text) for ICM20608_Init
    main.o(.text) refers to oled.o(.text) for OLED_Init
    main.o(.text) refers to oled_app.o(.text) for Init
    main.o(.text) refers to motor_app.o(.text) for dual_motor_init
    main.o(.text) refers to pid_app.o(.text) for pid_app_init
    main.o(.text) refers to scheduler.o(.text) for scheduler_init
    main.o(.text) refers to main.o(.data) for system_counter
    main.o(.data) refers to main.o(.conststring) for .conststring
    uartstdio.o(.text) refers to uartstdio.o(.constdata) for g_ui32UARTPeriph
    uartstdio.o(.text) refers to uartstdio.o(.data) for g_ui32Base
    uartstdio.o(.constdata) refers to uartstdio.o(.conststring) for .conststring
    startup_rvmdk.o(RESET) refers to startup_rvmdk.o(STACK) for StackMem
    startup_rvmdk.o(RESET) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    uart_app.o(.text) refers to uart.o(.text) for UARTCharPut
    uart_app.o(.text) refers to udma.o(.text) for uDMAChannelModeGet
    uart_app.o(.text) refers to printfa.o(i.__0printf) for __2printf
    uart_app.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    uart_app.o(.text) refers to sysctl.o(.text) for SysCtlPeripheralEnable
    uart_app.o(.text) refers to uartstdio.o(.text) for UARTStdioConfig
    uart_app.o(.text) refers to interrupt.o(.text) for IntPrioritySet
    uart_app.o(.text) refers to icm20608.o(.text) for MPU6050_Read_Data
    uart_app.o(.text) refers to imu.o(.text) for imu_update
    uart_app.o(.text) refers to pid_app.o(.text) for pid_app_task
    uart_app.o(.text) refers to timer.o(.text) for TimerIntClear
    uart_app.o(.text) refers to uart_app.o(.data) for rxBuffer0
    uart_app.o(.text) refers to uart_app.o(.bss) for uart_rx_buffer
    uart_app.o(.text) refers to icm20608.o(.bss) for gyro_offset
    uart_app.o(.data) refers to uart_app.o(.bss) for controlTableSpace
    scheduler.o(.text) refers to scheduler.o(.data) for task_num
    scheduler.o(.text) refers to uart_app.o(.data) for systick
    scheduler.o(.data) refers to adc_app.o(.text) for Adc_Proc
    scheduler.o(.data) refers to key_app.o(.text) for Key_Proc
    key_app.o(.text) refers to sysctl.o(.text) for SysCtlPeripheralEnable
    key_app.o(.text) refers to gpio.o(.text) for GPIODirModeSet
    key_app.o(.text) refers to printfa.o(i.__0printf) for __2printf
    key_app.o(.text) refers to pid_app.o(.text) for pid_app_start
    key_app.o(.text) refers to key_app.o(.data) for Key_Val
    adc_app.o(.text) refers to qei_app.o(.text) for QEIApp_GetData
    adc_app.o(.text) refers to sysctl.o(.text) for SysCtlPeripheralEnable
    adc_app.o(.text) refers to timer.o(.text) for TimerConfigure
    adc_app.o(.text) refers to adc.o(.text) for ADCIntClear
    adc_app.o(.text) refers to udma.o(.text) for uDMAChannelModeGet
    adc_app.o(.text) refers to f2d.o(.text) for __aeabi_f2d
    adc_app.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    adc_app.o(.text) refers to dadd.o(.text) for __aeabi_dadd
    adc_app.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    adc_app.o(.text) refers to gpio.o(.text) for GPIOPinTypeADC
    adc_app.o(.text) refers to interrupt.o(.text) for IntEnable
    adc_app.o(.text) refers to adc_app.o(.bss) for adcBuffer0
    adc_app.o(.text) refers to adc_app.o(.data) for Battery_Voltage
    adc_app.o(.text) refers to uart_app.o(.data) for controlTable
    myiic.o(.text) refers to sysctl.o(.text) for SysCtlPeripheralEnable
    myiic.o(.text) refers to sysctl.o(.emb_text) for SysCtlDelay
    myiic.o(.text) refers to gpio.o(.text) for GPIOPinConfigure
    myiic.o(.text) refers to i2c.o(.text) for I2CMasterInitExpClk
    icm20608.o(.text) refers to systicktime.o(.text) for delay_ms
    icm20608.o(.text) refers to myiic.o(.text) for Double_ReadI2C
    icm20608.o(.text) refers to f2d.o(.text) for __aeabi_f2d
    icm20608.o(.text) refers to dadd.o(.text) for __aeabi_dadd
    icm20608.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    icm20608.o(.text) refers to icm20608.o(.data) for g_Gyro_yoffset
    icm20608.o(.text) refers to icm20608.o(.bss) for gyro_offset
    systicktime.o(.text) refers to sysctl.o(.text) for SysCtlClockGet
    systicktime.o(.text) refers to systick.o(.text) for SysTickPeriodSet
    systicktime.o(.text) refers to systicktime.o(.data) for counter
    imu.o(.text) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    imu.o(.text) refers to imu.o(.data) for adaptive_filter
    imu.o(.text) refers to imu.o(.bss) for rMat
    imu.o(.text) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    imu.o(.text) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    imu.o(.text) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    imu.o(.text) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    oled.o(.text) refers to sysctl.o(.text) for SysCtlPeripheralEnable
    oled.o(.text) refers to gpio.o(.text) for GPIOPinTypeGPIOOutput
    oled.o(.text) refers to systicktime.o(.text) for Delay_Ms
    oled.o(.text) refers to oled.o(.constdata) for F6x8
    oled.o(.text) refers to ssd1306.o(.text) for ssd1306_begin
    ssd1306.o(.text) refers to oled.o(.text) for LCD_WrCmd
    ssd1306.o(.text) refers to ssd1306.o(.data) for _width
    ssd1306.o(.text) refers to memseta.o(.text) for __aeabi_memclr
    ssd1306.o(.text) refers to glcdfont.o(.constdata) for font
    oled_app.o(.text) refers to ssd1306.o(.text) for ssd1306_clear_display
    oled_app.o(.text) refers to printfa.o(i.__0vsprintf) for vsprintf
    oled_app.o(.text) refers to qei_app.o(.text) for QEIApp_GetData
    qei_app.o(.text) refers to qei.o(.text) for QEIIntClear
    qei_app.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    qei_app.o(.text) refers to sysctl.o(.text) for SysCtlPeripheralEnable
    qei_app.o(.text) refers to gpio.o(.text) for GPIOPinConfigure
    qei_app.o(.text) refers to interrupt.o(.text) for IntEnable
    qei_app.o(.text) refers to qei_app.o(.bss) for qei_data
    pwm_app.o(.text) refers to sysctl.o(.text) for SysCtlPWMClockSet
    pwm_app.o(.text) refers to sysctl.o(.emb_text) for SysCtlDelay
    pwm_app.o(.text) refers to gpio.o(.text) for GPIOPinConfigure
    pwm_app.o(.text) refers to pwm.o(.text) for PWMGenConfigure
    pwm_debug.o(.text) refers to printfa.o(i.__0printf) for __2printf
    pwm_debug.o(.text) refers to pwm_app.o(.text) for PWM_APP_GetChannelConfig
    pwm_debug.o(.text) refers to pwm.o(.text) for PWMGenPeriodGet
    pwm_debug.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    pwm_debug.o(.text) refers to printfa.o(i.__0snprintf) for __2snprintf
    motor_app.o(.text) refers to pwm_app.o(.text) for PWM_APP_Init
    motor_app.o(.text) refers to motor_app.o(.data) for g_is_initialized
    motor_app.o(.text) refers to motor_app.o(.constdata) for .constdata
    pid_app.o(.text) refers to pid.o(.text) for pid_init
    pid_app.o(.text) refers to qei_app.o(.text) for QEIApp_GetData
    pid_app.o(.text) refers to motor_app.o(.text) for my_left_motor_set_speed
    pid_app.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy4
    pid_app.o(.text) refers to pid_app.o(.data) for pid_params_left
    pid_app.o(.text) refers to pid_app.o(.bss) for pid_left
    asinf.o(i.__hardfp_asinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf.o(i.__hardfp_asinf) refers to sqrtf.o(i.sqrtf) for sqrtf
    asinf.o(i.__hardfp_asinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    asinf.o(i.__hardfp_asinf) refers to errno.o(i.__set_errno) for __set_errno
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    asinf.o(i.__softfp_asinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf.o(i.__softfp_asinf) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    asinf.o(i.asinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf.o(i.asinf) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    asinf_x.o(i.____hardfp_asinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf_x.o(i.____hardfp_asinf$lsc) refers to sqrtf.o(i.sqrtf) for sqrtf
    asinf_x.o(i.____hardfp_asinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    asinf_x.o(i.____hardfp_asinf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    asinf_x.o(i.____softfp_asinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf_x.o(i.____softfp_asinf$lsc) refers to asinf_x.o(i.____hardfp_asinf$lsc) for ____hardfp_asinf$lsc
    asinf_x.o(i.__asinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf_x.o(i.__asinf$lsc) refers to asinf_x.o(i.____hardfp_asinf$lsc) for ____hardfp_asinf$lsc
    atan2f.o(i.__hardfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to errno.o(i.__set_errno) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    cosf.o(i.__hardfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to errno.o(i.__set_errno) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf_x.o(i.____hardfp_cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.____hardfp_cosf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf_x.o(i.____hardfp_cosf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    cosf_x.o(i.____hardfp_cosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf_x.o(i.____softfp_cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.____softfp_cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    cosf_x.o(i.__cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.__cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    sinf.o(i.__hardfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to errno.o(i.__set_errno) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to uart_app.o(.text) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to uart_app.o(.text) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to uart_app.o(.text) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to uart_app.o(.text) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to uart_app.o(.text) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to uart_app.o(.text) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to uart_app.o(.text) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to uart_app.o(.text) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to uart_app.o(.text) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to uart_app.o(.text) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to uart_app.o(.text) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to uart_app.o(.text) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to uart_app.o(.text) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to uart_app.o(.text) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to uart_app.o(.text) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to uart_app.o(.text) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to uart_app.o(.text) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to uart_app.o(.text) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to uart_app.o(.text) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to uart_app.o(.text) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to uart_app.o(.text) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to uart_app.o(.text) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to uart_app.o(.text) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to uart_app.o(.text) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to uart_app.o(.text) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to uart_app.o(.text) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to uart_app.o(.text) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to uart_app.o(.text) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to uart_app.o(.text) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to uart_app.o(.text) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to uart_app.o(.text) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to uart_app.o(.text) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to uart_app.o(.text) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to uart_app.o(.text) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to uart_app.o(.text) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to uart_app.o(.text) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to uart_app.o(.text) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to uart_app.o(.text) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to uart_app.o(.text) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to uart_app.o(.text) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to uart_app.o(.text) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to uart_app.o(.text) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to uart_app.o(.text) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to uart_app.o(.text) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_rvmdk.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_rvmdk.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing aes.o(.text), (864 bytes).
    Removing can.o(.text), (1628 bytes).
    Removing can.o(.constdata), (32 bytes).
    Removing comp.o(.text), (162 bytes).
    Removing crc.o(.text), (102 bytes).
    Removing des.o(.text), (448 bytes).
    Removing eeprom.o(.text), (892 bytes).
    Removing emac.o(.text), (2044 bytes).
    Removing emac.o(.constdata), (24 bytes).
    Removing epi.o(.text), (800 bytes).
    Removing flash.o(.text), (616 bytes).
    Removing flash.o(.constdata), (128 bytes).
    Removing fpu.o(.text), (164 bytes).
    Removing hibernate.o(.text), (2004 bytes).
    Removing lcd.o(.text), (832 bytes).
    Removing mpu.o(.text), (180 bytes).
    Removing shamd5.o(.text), (832 bytes).
    Removing ssi.o(.text), (488 bytes).
    Removing ssi.o(.constdata), (64 bytes).
    Removing sw_crc.o(.text), (724 bytes).
    Removing sw_crc.o(.constdata), (1792 bytes).
    Removing sysexc.o(.text), (156 bytes).
    Removing usb.o(.text), (3742 bytes).
    Removing watchdog.o(.text), (200 bytes).
    Removing startup_rvmdk.o(HEAP), (0 bytes).
    Removing startup_rvmdk.o(.text), (0 bytes).
    Removing epi.o(i.EPIWorkaroundWordWrite), (12 bytes).
    Removing epi.o(i.EPIWorkaroundWordRead), (14 bytes).
    Removing epi.o(i.EPIWorkaroundHWordWrite), (12 bytes).
    Removing epi.o(i.EPIWorkaroundByteWrite), (12 bytes).
    Removing epi.o(i.EPIWorkaroundByteRead), (14 bytes).

31 unused section(s) (total 18982 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    RESET                                    0x00000000   Section      648  startup_rvmdk.o(RESET)
    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../mathlib/asinf.c                       0x00000000   Number         0  asinf_x.o ABSOLUTE
    ../mathlib/asinf.c                       0x00000000   Number         0  asinf.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf_x.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ..\..\Libraries\driverlib\adc.c          0x00000000   Number         0  adc.o ABSOLUTE
    ..\..\Libraries\driverlib\aes.c          0x00000000   Number         0  aes.o ABSOLUTE
    ..\..\Libraries\driverlib\can.c          0x00000000   Number         0  can.o ABSOLUTE
    ..\..\Libraries\driverlib\comp.c         0x00000000   Number         0  comp.o ABSOLUTE
    ..\..\Libraries\driverlib\cpu.c          0x00000000   Number         0  cpu.o ABSOLUTE
    ..\..\Libraries\driverlib\crc.c          0x00000000   Number         0  crc.o ABSOLUTE
    ..\..\Libraries\driverlib\des.c          0x00000000   Number         0  des.o ABSOLUTE
    ..\..\Libraries\driverlib\eeprom.c       0x00000000   Number         0  eeprom.o ABSOLUTE
    ..\..\Libraries\driverlib\emac.c         0x00000000   Number         0  emac.o ABSOLUTE
    ..\..\Libraries\driverlib\epi.c          0x00000000   Number         0  epi.o ABSOLUTE
    ..\..\Libraries\driverlib\flash.c        0x00000000   Number         0  flash.o ABSOLUTE
    ..\..\Libraries\driverlib\fpu.c          0x00000000   Number         0  fpu.o ABSOLUTE
    ..\..\Libraries\driverlib\gpio.c         0x00000000   Number         0  gpio.o ABSOLUTE
    ..\..\Libraries\driverlib\hibernate.c    0x00000000   Number         0  hibernate.o ABSOLUTE
    ..\..\Libraries\driverlib\i2c.c          0x00000000   Number         0  i2c.o ABSOLUTE
    ..\..\Libraries\driverlib\interrupt.c    0x00000000   Number         0  interrupt.o ABSOLUTE
    ..\..\Libraries\driverlib\lcd.c          0x00000000   Number         0  lcd.o ABSOLUTE
    ..\..\Libraries\driverlib\mpu.c          0x00000000   Number         0  mpu.o ABSOLUTE
    ..\..\Libraries\driverlib\pwm.c          0x00000000   Number         0  pwm.o ABSOLUTE
    ..\..\Libraries\driverlib\qei.c          0x00000000   Number         0  qei.o ABSOLUTE
    ..\..\Libraries\driverlib\shamd5.c       0x00000000   Number         0  shamd5.o ABSOLUTE
    ..\..\Libraries\driverlib\ssi.c          0x00000000   Number         0  ssi.o ABSOLUTE
    ..\..\Libraries\driverlib\sw_crc.c       0x00000000   Number         0  sw_crc.o ABSOLUTE
    ..\..\Libraries\driverlib\sysctl.c       0x00000000   Number         0  sysctl.o ABSOLUTE
    ..\..\Libraries\driverlib\sysexc.c       0x00000000   Number         0  sysexc.o ABSOLUTE
    ..\..\Libraries\driverlib\systick.c      0x00000000   Number         0  systick.o ABSOLUTE
    ..\..\Libraries\driverlib\timer.c        0x00000000   Number         0  timer.o ABSOLUTE
    ..\..\Libraries\driverlib\uart.c         0x00000000   Number         0  uart.o ABSOLUTE
    ..\..\Libraries\driverlib\udma.c         0x00000000   Number         0  udma.o ABSOLUTE
    ..\..\Libraries\driverlib\usb.c          0x00000000   Number         0  usb.o ABSOLUTE
    ..\..\Libraries\driverlib\watchdog.c     0x00000000   Number         0  watchdog.o ABSOLUTE
    ..\..\Libraries\utils\uartstdio.c        0x00000000   Number         0  uartstdio.o ABSOLUTE
    ..\..\User\OLED.c                        0x00000000   Number         0  oled.o ABSOLUTE
    ..\..\User\SystickTime.c                 0x00000000   Number         0  systicktime.o ABSOLUTE
    ..\..\User\adc_app.c                     0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\..\User\glcdfont.c                    0x00000000   Number         0  glcdfont.o ABSOLUTE
    ..\..\User\icm20608.c                    0x00000000   Number         0  icm20608.o ABSOLUTE
    ..\..\User\imu.c                         0x00000000   Number         0  imu.o ABSOLUTE
    ..\..\User\key_app.c                     0x00000000   Number         0  key_app.o ABSOLUTE
    ..\..\User\main.c                        0x00000000   Number         0  main.o ABSOLUTE
    ..\..\User\motor_app.c                   0x00000000   Number         0  motor_app.o ABSOLUTE
    ..\..\User\myiic.c                       0x00000000   Number         0  myiic.o ABSOLUTE
    ..\..\User\oled_app.c                    0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\..\User\pid.c                         0x00000000   Number         0  pid.o ABSOLUTE
    ..\..\User\pid_app.c                     0x00000000   Number         0  pid_app.o ABSOLUTE
    ..\..\User\pwm_app.c                     0x00000000   Number         0  pwm_app.o ABSOLUTE
    ..\..\User\pwm_debug.c                   0x00000000   Number         0  pwm_debug.o ABSOLUTE
    ..\..\User\qei_app.c                     0x00000000   Number         0  qei_app.o ABSOLUTE
    ..\..\User\scheduler.c                   0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\..\User\ssd1306.c                     0x00000000   Number         0  ssd1306.o ABSOLUTE
    ..\..\User\startup_rvmdk.S               0x00000000   Number         0  startup_rvmdk.o ABSOLUTE
    ..\..\User\uart_app.c                    0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\\..\\Libraries\\driverlib\\cpu.c      0x00000000   Number         0  cpu.o ABSOLUTE
    ..\\..\\Libraries\\driverlib\\sysctl.c   0x00000000   Number         0  sysctl.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    $v0                                      0x0000026c   Number         0  startup_rvmdk.o(RESET)
    NmiSR                                    0x00000281   Thumb Code     0  startup_rvmdk.o(RESET)
    FaultISR                                 0x00000283   Thumb Code     0  startup_rvmdk.o(RESET)
    IntDefaultHandler                        0x00000285   Thumb Code     0  startup_rvmdk.o(RESET)
    .ARM.Collect$$$$00000000                 0x00000288   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x00000288   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0000028c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x00000290   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x00000290   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x00000290   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x00000298   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0000029c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0000029c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0000029c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0000029c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x000002a0   Section       38  cpu.o(.emb_text)
    $v0                                      0x000002a0   Number         0  cpu.o(.emb_text)
    .emb_text                                0x000002c8   Section        6  sysctl.o(.emb_text)
    $v0                                      0x000002c8   Number         0  sysctl.o(.emb_text)
    .text                                    0x000002d0   Section        0  adc.o(.text)
    _ADCIntNumberGet                         0x000002d1   Thumb Code    76  adc.o(.text)
    .text                                    0x00000694   Section        0  gpio.o(.text)
    _GPIOIntNumberGet                        0x00000695   Thumb Code    56  gpio.o(.text)
    .text                                    0x00000e8c   Section        0  i2c.o(.text)
    _I2CIntNumberGet                         0x00000e8d   Thumb Code    56  i2c.o(.text)
    .text                                    0x00001214   Section        0  interrupt.o(.text)
    _IntDefaultHandler                       0x00001215   Thumb Code     4  interrupt.o(.text)
    .text                                    0x00001544   Section        0  pwm.o(.text)
    _PWMGenIntNumberGet                      0x00001677   Thumb Code   248  pwm.o(.text)
    _PWMFaultIntNumberGet                    0x000017b3   Thumb Code    62  pwm.o(.text)
    .text                                    0x000019a4   Section        0  qei.o(.text)
    _QEIIntNumberGet                         0x00001a19   Thumb Code    62  qei.o(.text)
    .text                                    0x00001ac4   Section        0  sysctl.o(.text)
    _SysCtlMemTimingGet                      0x00001ac5   Thumb Code    36  sysctl.o(.text)
    _SysCtlFrequencyGet                      0x00001ae9   Thumb Code    96  sysctl.o(.text)
    .text                                    0x0000261c   Section        0  systick.o(.text)
    .text                                    0x000026b8   Section        0  timer.o(.text)
    _TimerIntNumberGet                       0x000026b9   Thumb Code    68  timer.o(.text)
    .text                                    0x000029b0   Section        0  uart.o(.text)
    _UARTIntNumberGet                        0x000029b1   Thumb Code    56  uart.o(.text)
    .text                                    0x00002ce0   Section        0  udma.o(.text)
    .text                                    0x0000300c   Section        0  main.o(.text)
    .text                                    0x00003250   Section        0  uartstdio.o(.text)
    .text                                    0x00003608   Section        0  uart_app.o(.text)
    .text                                    0x00003a04   Section        0  scheduler.o(.text)
    .text                                    0x00003a64   Section        0  key_app.o(.text)
    .text                                    0x00003b88   Section        0  adc_app.o(.text)
    .text                                    0x00003ed0   Section        0  myiic.o(.text)
    .text                                    0x0000436c   Section        0  icm20608.o(.text)
    .text                                    0x000045e8   Section        0  systicktime.o(.text)
    SycTickHandler                           0x000045e9   Thumb Code    12  systicktime.o(.text)
    .text                                    0x000046d8   Section        0  imu.o(.text)
    compute_rotation_matrix                  0x000047f5   Thumb Code   358  imu.o(.text)
    .text                                    0x00005238   Section        0  oled.o(.text)
    .text                                    0x00005f8c   Section        0  ssd1306.o(.text)
    .text                                    0x000072d4   Section        0  oled_app.o(.text)
    .text                                    0x00007368   Section        0  qei_app.o(.text)
    QEIApp_QEI0IntHandler                    0x00007383   Thumb Code    90  qei_app.o(.text)
    QEIApp_QEI1IntHandler                    0x000073dd   Thumb Code    86  qei_app.o(.text)
    .text                                    0x00007618   Section        0  pwm_app.o(.text)
    .text                                    0x00007a84   Section        0  pwm_debug.o(.text)
    get_gen_for_channel                      0x00007a85   Thumb Code    50  pwm_debug.o(.text)
    .text                                    0x00007d78   Section        0  motor_app.o(.text)
    .text                                    0x00008108   Section        0  pid.o(.text)
    pid_out_limit                            0x000081ab   Thumb Code    64  pid.o(.text)
    pid_formula_positional                   0x000081eb   Thumb Code   122  pid.o(.text)
    pid_formula_incremental                  0x0000828b   Thumb Code   142  pid.o(.text)
    .text                                    0x00008344   Section        0  pid_app.o(.text)
    constrain                                0x00008345   Thumb Code    42  pid_app.o(.text)
    pid_app_limit_integral                   0x0000836f   Thumb Code    40  pid_app.o(.text)
    .text                                    0x000085cc   Section        0  memcpya.o(.text)
    .text                                    0x000085f0   Section        0  memseta.o(.text)
    .text                                    0x00008614   Section        0  dadd.o(.text)
    .text                                    0x00008762   Section        0  dmul.o(.text)
    .text                                    0x00008846   Section        0  f2d.o(.text)
    .text                                    0x0000886c   Section        0  d2f.o(.text)
    .text                                    0x000088a4   Section        0  uidiv.o(.text)
    .text                                    0x000088d0   Section        0  uldiv.o(.text)
    .text                                    0x00008932   Section        0  llshl.o(.text)
    .text                                    0x00008950   Section        0  llsshr.o(.text)
    .text                                    0x00008974   Section        0  iusefp.o(.text)
    .text                                    0x00008974   Section        0  fepilogue.o(.text)
    .text                                    0x000089e2   Section        0  depilogue.o(.text)
    .text                                    0x00008a9c   Section        0  ddiv.o(.text)
    .text                                    0x00008b7a   Section        0  dfixul.o(.text)
    .text                                    0x00008bac   Section       48  cdrcmple.o(.text)
    .text                                    0x00008bdc   Section       36  init.o(.text)
    .text                                    0x00008c00   Section        0  llushr.o(.text)
    .text                                    0x00008c20   Section        0  __dczerorl2.o(.text)
    i.__0printf                              0x00008c78   Section        0  printfa.o(i.__0printf)
    i.__0snprintf                            0x00008c98   Section        0  printfa.o(i.__0snprintf)
    i.__0vsprintf                            0x00008ccc   Section        0  printfa.o(i.__0vsprintf)
    i.__ARM_fpclassifyf                      0x00008cf0   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__hardfp_asinf                         0x00008d18   Section        0  asinf.o(i.__hardfp_asinf)
    i.__hardfp_atan2f                        0x00008e44   Section        0  atan2f.o(i.__hardfp_atan2f)
    i.__hardfp_cosf                          0x000090f0   Section        0  cosf.o(i.__hardfp_cosf)
    i.__hardfp_sinf                          0x00009240   Section        0  sinf.o(i.__hardfp_sinf)
    i.__hardfp_sqrtf                         0x000093d0   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_infnan                   0x0000940a   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_infnan2                  0x00009410   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_invalid                  0x00009418   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x00009428   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x00009438   Section        0  rredf.o(i.__mathlib_rredf2)
    i.__scatterload_copy                     0x0000958c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0000959a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0000959c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x000095ac   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x000095b8   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x000095b9   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x0000973c   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x0000973d   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x00009df0   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x00009df1   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x00009e14   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x00009e15   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x00009e42   Section        0  printfa.o(i._snputc)
    _snputc                                  0x00009e43   Thumb Code    22  printfa.o(i._snputc)
    i._sputc                                 0x00009e58   Section        0  printfa.o(i._sputc)
    _sputc                                   0x00009e59   Thumb Code    10  printfa.o(i._sputc)
    i.sqrtf                                  0x00009e62   Section        0  sqrtf.o(i.sqrtf)
    .constdata                               0x00009ea0   Section      528  gpio.o(.constdata)
    g_ppui32GPIOIntMapBlizzard               0x00009ea0   Data         192  gpio.o(.constdata)
    g_ppui32GPIOIntMapSnowflake              0x00009f60   Data         192  gpio.o(.constdata)
    g_pui32GPIOBaseAddrs                     0x0000a020   Data         144  gpio.o(.constdata)
    .constdata                               0x0000a0b0   Section      128  i2c.o(.constdata)
    g_ppui32I2CIntMap                        0x0000a0b0   Data          48  i2c.o(.constdata)
    g_ppui32I2CIntMapSnowflake               0x0000a0e0   Data          80  i2c.o(.constdata)
    .constdata                               0x0000a130   Section      268  interrupt.o(.constdata)
    g_pui32Priority                          0x0000a130   Data          32  interrupt.o(.constdata)
    g_pui32Regs                              0x0000a150   Data         156  interrupt.o(.constdata)
    g_pui32EnRegs                            0x0000a1ec   Data          20  interrupt.o(.constdata)
    g_pui32Dii16Regs                         0x0000a200   Data          20  interrupt.o(.constdata)
    g_pui32PendRegs                          0x0000a214   Data          20  interrupt.o(.constdata)
    g_pui32UnpendRegs                        0x0000a228   Data          20  interrupt.o(.constdata)
    .constdata                               0x0000a23c   Section      460  sysctl.o(.constdata)
    g_pui32Xtals                             0x0000a23c   Data         108  sysctl.o(.constdata)
    g_pppui32XTALtoVCO                       0x0000a2a8   Data         288  sysctl.o(.constdata)
    g_sXTALtoMEMTIM                          0x0000a3c8   Data          56  sysctl.o(.constdata)
    g_pui32VCOFrequencies                    0x0000a400   Data           8  sysctl.o(.constdata)
    .constdata                               0x0000a408   Section      160  timer.o(.constdata)
    g_ppui32TimerIntMap                      0x0000a408   Data          96  timer.o(.constdata)
    g_ppui32TimerIntMapSnowflake             0x0000a468   Data          64  timer.o(.constdata)
    .constdata                               0x0000a4a8   Section      128  uart.o(.constdata)
    g_ppui32UARTIntMap                       0x0000a4a8   Data          64  uart.o(.constdata)
    g_ppui32UARTIntMapSnowflake              0x0000a4e8   Data          64  uart.o(.constdata)
    .constdata                               0x0000a528   Section       28  uartstdio.o(.constdata)
    g_pcHex                                  0x0000a528   Data           4  uartstdio.o(.constdata)
    g_ui32UARTBase                           0x0000a52c   Data          12  uartstdio.o(.constdata)
    g_ui32UARTPeriph                         0x0000a538   Data          12  uartstdio.o(.constdata)
    .constdata                               0x0000a544   Section     1275  glcdfont.o(.constdata)
    .constdata                               0x0000aa3f   Section     8552  oled.o(.constdata)
    .constdata                               0x0000cba8   Section       16  motor_app.o(.constdata)
    .constdata                               0x0000cbb8   Section       32  rredf.o(.constdata)
    twooverpi                                0x0000cbb8   Data          32  rredf.o(.constdata)
    .conststring                             0x0000cbd8   Section       10  main.o(.conststring)
    .conststring                             0x0000cbe4   Section       17  uartstdio.o(.conststring)
    .data                                    0x20000000   Section        3  adc.o(.data)
    g_pui8OversampleFactor                   0x20000000   Data           3  adc.o(.data)
    .data                                    0x20000008   Section       40  main.o(.data)
    .data                                    0x20000030   Section        5  uartstdio.o(.data)
    g_ui32Base                               0x20000030   Data           4  uartstdio.o(.data)
    bLastWasCR                               0x20000034   Data           1  uartstdio.o(.data)
    .data                                    0x20000038   Section       18  uart_app.o(.data)
    .data                                    0x2000004c   Section       28  scheduler.o(.data)
    scheduler_task                           0x20000050   Data          24  scheduler.o(.data)
    .data                                    0x20000068   Section        4  key_app.o(.data)
    .data                                    0x2000006c   Section        4  adc_app.o(.data)
    .data                                    0x20000070   Section       16  icm20608.o(.data)
    .data                                    0x20000080   Section        4  systicktime.o(.data)
    counter                                  0x20000080   Data           4  systicktime.o(.data)
    .data                                    0x20000084   Section      100  imu.o(.data)
    beta                                     0x20000084   Data           4  imu.o(.data)
    Kp                                       0x20000088   Data           4  imu.o(.data)
    Ki                                       0x2000008c   Data           4  imu.o(.data)
    exInt                                    0x20000090   Data           4  imu.o(.data)
    eyInt                                    0x20000094   Data           4  imu.o(.data)
    ezInt                                    0x20000098   Data           4  imu.o(.data)
    q0                                       0x2000009c   Data           4  imu.o(.data)
    q1                                       0x200000a0   Data           4  imu.o(.data)
    q2                                       0x200000a4   Data           4  imu.o(.data)
    q3                                       0x200000a8   Data           4  imu.o(.data)
    last_yaw                                 0x200000ac   Data           4  imu.o(.data)
    adaptive_filter                          0x200000b0   Data          12  imu.o(.data)
    yaw_ekf                                  0x200000bc   Data          40  imu.o(.data)
    last_filtered_yaw                        0x200000e4   Data           4  imu.o(.data)
    .data                                    0x200000e8   Section     1063  ssd1306.o(.data)
    _vccstate                                0x200000e8   Data           1  ssd1306.o(.data)
    _width                                   0x200000ea   Data           2  ssd1306.o(.data)
    _height                                  0x200000ec   Data           2  ssd1306.o(.data)
    WIDTH                                    0x200000ee   Data           2  ssd1306.o(.data)
    HEIGHT                                   0x200000f0   Data           2  ssd1306.o(.data)
    cursor_x                                 0x200000f2   Data           2  ssd1306.o(.data)
    cursor_y                                 0x200000f4   Data           2  ssd1306.o(.data)
    textsize                                 0x200000f6   Data           1  ssd1306.o(.data)
    rotation                                 0x200000f7   Data           1  ssd1306.o(.data)
    textcolor                                0x200000f8   Data           2  ssd1306.o(.data)
    textbgcolor                              0x200000fa   Data           2  ssd1306.o(.data)
    use_i2c                                  0x200000fe   Data           1  ssd1306.o(.data)
    buffer                                   0x200000ff   Data        1024  ssd1306.o(.data)
    premask                                  0x200004ff   Data           8  ssd1306.o(.data)
    postmask                                 0x20000507   Data           8  ssd1306.o(.data)
    .data                                    0x2000050f   Section        1  motor_app.o(.data)
    g_is_initialized                         0x2000050f   Data           1  motor_app.o(.data)
    .data                                    0x20000510   Section       93  pid_app.o(.data)
    pid_running                              0x20000568   Data           1  pid_app.o(.data)
    .data                                    0x20000570   Section        4  stdout.o(.data)
    .data                                    0x20000574   Section        4  errno.o(.data)
    _errno                                   0x20000574   Data           4  errno.o(.data)
    vtable                                   0x20000800   Section      620  interrupt.o(vtable)
    g_pfnRAMVectors                          0x20000800   Data         620  interrupt.o(vtable)
    .bss                                     0x20000c00   Section     1288  uart_app.o(.bss)
    .bss                                     0x20001108   Section      104  adc_app.o(.bss)
    .bss                                     0x20001170   Section       12  icm20608.o(.bss)
    .bss                                     0x2000117c   Section       36  imu.o(.bss)
    rMat                                     0x2000117c   Data          36  imu.o(.bss)
    .bss                                     0x200011a0   Section       32  qei_app.o(.bss)
    qei_data                                 0x200011a0   Data          32  qei_app.o(.bss)
    .bss                                     0x200011c0   Section      120  pid_app.o(.bss)
    STACK                                    0x20001238   Section      512  startup_rvmdk.o(STACK)
    StackMem                                 0x20001238   Data           0  startup_rvmdk.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __Vectors                                0x00000000   Data           0  startup_rvmdk.o(RESET)
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    Reset_Handler                            0x0000026d   Thumb Code     0  startup_rvmdk.o(RESET)
    __main                                   0x00000289   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x00000289   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0000028d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x00000291   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x00000291   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x00000291   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x00000291   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x00000299   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0000029d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0000029d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    CPUcpsid                                 0x000002a1   Thumb Code     8  cpu.o(.emb_text)
    CPUprimask                               0x000002a9   Thumb Code     6  cpu.o(.emb_text)
    CPUcpsie                                 0x000002af   Thumb Code     8  cpu.o(.emb_text)
    CPUwfi                                   0x000002b7   Thumb Code     4  cpu.o(.emb_text)
    CPUbasepriSet                            0x000002bb   Thumb Code     6  cpu.o(.emb_text)
    CPUbasepriGet                            0x000002c1   Thumb Code     6  cpu.o(.emb_text)
    SysCtlDelay                              0x000002c9   Thumb Code     6  sysctl.o(.emb_text)
    ADCIntRegister                           0x0000031d   Thumb Code    38  adc.o(.text)
    ADCIntUnregister                         0x00000343   Thumb Code    30  adc.o(.text)
    ADCIntDisable                            0x00000361   Thumb Code    12  adc.o(.text)
    ADCIntEnable                             0x0000036d   Thumb Code    18  adc.o(.text)
    ADCIntStatus                             0x0000037f   Thumb Code    54  adc.o(.text)
    ADCIntClear                              0x000003b5   Thumb Code     8  adc.o(.text)
    ADCSequenceEnable                        0x000003bd   Thumb Code    12  adc.o(.text)
    ADCSequenceDisable                       0x000003c9   Thumb Code    12  adc.o(.text)
    ADCSequenceConfigure                     0x000003d5   Thumb Code    44  adc.o(.text)
    ADCSequenceStepConfigure                 0x00000401   Thumb Code   118  adc.o(.text)
    ADCSequenceOverflow                      0x00000477   Thumb Code    12  adc.o(.text)
    ADCSequenceOverflowClear                 0x00000483   Thumb Code     8  adc.o(.text)
    ADCSequenceUnderflow                     0x0000048b   Thumb Code    12  adc.o(.text)
    ADCSequenceUnderflowClear                0x00000497   Thumb Code     8  adc.o(.text)
    ADCSequenceDataGet                       0x0000049f   Thumb Code    34  adc.o(.text)
    ADCProcessorTrigger                      0x000004c1   Thumb Code    24  adc.o(.text)
    ADCSoftwareOversampleConfigure           0x000004d9   Thumb Code    24  adc.o(.text)
    ADCSoftwareOversampleStepConfigure       0x000004f1   Thumb Code   110  adc.o(.text)
    ADCSoftwareOversampleDataGet             0x0000055f   Thumb Code    54  adc.o(.text)
    ADCHardwareOversampleConfigure           0x00000595   Thumb Code    18  adc.o(.text)
    ADCComparatorConfigure                   0x000005a7   Thumb Code    10  adc.o(.text)
    ADCComparatorRegionSet                   0x000005b1   Thumb Code    16  adc.o(.text)
    ADCComparatorReset                       0x000005c1   Thumb Code    34  adc.o(.text)
    ADCComparatorIntDisable                  0x000005e3   Thumb Code    14  adc.o(.text)
    ADCComparatorIntEnable                   0x000005f1   Thumb Code    14  adc.o(.text)
    ADCComparatorIntStatus                   0x000005ff   Thumb Code     6  adc.o(.text)
    ADCComparatorIntClear                    0x00000605   Thumb Code     4  adc.o(.text)
    ADCIntDisableEx                          0x00000609   Thumb Code     8  adc.o(.text)
    ADCIntEnableEx                           0x00000611   Thumb Code     8  adc.o(.text)
    ADCIntStatusEx                           0x00000619   Thumb Code    22  adc.o(.text)
    ADCIntClearEx                            0x0000062f   Thumb Code     8  adc.o(.text)
    ADCReferenceSet                          0x00000637   Thumb Code    12  adc.o(.text)
    ADCReferenceGet                          0x00000643   Thumb Code    10  adc.o(.text)
    ADCPhaseDelaySet                         0x0000064d   Thumb Code     4  adc.o(.text)
    ADCPhaseDelayGet                         0x00000651   Thumb Code     6  adc.o(.text)
    ADCSequenceDMAEnable                     0x00000657   Thumb Code    14  adc.o(.text)
    ADCSequenceDMADisable                    0x00000665   Thumb Code    14  adc.o(.text)
    ADCBusy                                  0x00000673   Thumb Code    10  adc.o(.text)
    GPIODirModeSet                           0x000006cd   Thumb Code    50  gpio.o(.text)
    GPIODirModeGet                           0x000006ff   Thumb Code    46  gpio.o(.text)
    GPIOIntTypeSet                           0x0000072d   Thumb Code   102  gpio.o(.text)
    GPIOIntTypeGet                           0x00000793   Thumb Code    84  gpio.o(.text)
    GPIOPadConfigSet                         0x000007e7   Thumb Code   342  gpio.o(.text)
    GPIOPadConfigGet                         0x0000093d   Thumb Code   206  gpio.o(.text)
    GPIOIntEnable                            0x00000a0b   Thumb Code    12  gpio.o(.text)
    GPIOIntDisable                           0x00000a17   Thumb Code    12  gpio.o(.text)
    GPIOIntStatus                            0x00000a23   Thumb Code    16  gpio.o(.text)
    GPIOIntClear                             0x00000a33   Thumb Code     6  gpio.o(.text)
    GPIOIntRegister                          0x00000a39   Thumb Code    30  gpio.o(.text)
    GPIOIntUnregister                        0x00000a57   Thumb Code    26  gpio.o(.text)
    GPIOPinRead                              0x00000a71   Thumb Code    32  gpio.o(.text)
    GPIOPinWrite                             0x00000a91   Thumb Code    10  gpio.o(.text)
    GPIOPinTypeADC                           0x00000a9b   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeCAN                           0x00000ab9   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeComparator                    0x00000ad7   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeEPI                           0x00000af5   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeEthernetLED                   0x00000b13   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeEthernetMII                   0x00000b31   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeFan                           0x00000b4f   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeGPIOInput                     0x00000b6d   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeGPIOOutput                    0x00000b8b   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeGPIOOutputOD                  0x00000ba9   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeI2C                           0x00000bc7   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeI2CSCL                        0x00000be5   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeLCD                           0x00000c03   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeLPC                           0x00000c21   Thumb Code    30  gpio.o(.text)
    GPIOPinTypePECIRx                        0x00000c3f   Thumb Code    30  gpio.o(.text)
    GPIOPinTypePECITx                        0x00000c5d   Thumb Code    30  gpio.o(.text)
    GPIOPinTypePWM                           0x00000c7b   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeQEI                           0x00000c99   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeSSI                           0x00000cb7   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeTimer                         0x00000cd5   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeUART                          0x00000cf3   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeUSBAnalog                     0x00000d11   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeUSBDigital                    0x00000d2f   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeWakeHigh                      0x00000d4d   Thumb Code    32  gpio.o(.text)
    GPIOPinTypeWakeLow                       0x00000d6d   Thumb Code    32  gpio.o(.text)
    GPIOPinTypeKBRow                         0x00000d8d   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeKBColumn                      0x00000dab   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeLEDSeq                        0x00000dc9   Thumb Code    30  gpio.o(.text)
    GPIOPinTypeCIR                           0x00000de7   Thumb Code    30  gpio.o(.text)
    GPIOPinWakeStatus                        0x00000e05   Thumb Code     8  gpio.o(.text)
    GPIOPinConfigure                         0x00000e0d   Thumb Code    70  gpio.o(.text)
    GPIODMATriggerEnable                     0x00000e53   Thumb Code    12  gpio.o(.text)
    GPIODMATriggerDisable                    0x00000e5f   Thumb Code    12  gpio.o(.text)
    GPIOADCTriggerEnable                     0x00000e6b   Thumb Code    12  gpio.o(.text)
    GPIOADCTriggerDisable                    0x00000e77   Thumb Code    12  gpio.o(.text)
    I2CMasterEnable                          0x00000ec5   Thumb Code    10  i2c.o(.text)
    I2CMasterInitExpClk                      0x00000ecf   Thumb Code    78  i2c.o(.text)
    I2CSlaveEnable                           0x00000f1d   Thumb Code    16  i2c.o(.text)
    I2CSlaveInit                             0x00000f2d   Thumb Code    18  i2c.o(.text)
    I2CSlaveAddressSet                       0x00000f3f   Thumb Code    28  i2c.o(.text)
    I2CMasterDisable                         0x00000f5b   Thumb Code    10  i2c.o(.text)
    I2CSlaveDisable                          0x00000f65   Thumb Code    16  i2c.o(.text)
    I2CIntRegister                           0x00000f75   Thumb Code    30  i2c.o(.text)
    I2CIntUnregister                         0x00000f93   Thumb Code    26  i2c.o(.text)
    I2CMasterIntEnable                       0x00000fad   Thumb Code     6  i2c.o(.text)
    I2CMasterIntEnableEx                     0x00000fb3   Thumb Code     8  i2c.o(.text)
    I2CSlaveIntEnable                        0x00000fbb   Thumb Code    14  i2c.o(.text)
    I2CSlaveIntEnableEx                      0x00000fc9   Thumb Code    12  i2c.o(.text)
    I2CMasterIntDisable                      0x00000fd5   Thumb Code     6  i2c.o(.text)
    I2CMasterIntDisableEx                    0x00000fdb   Thumb Code     8  i2c.o(.text)
    I2CSlaveIntDisable                       0x00000fe3   Thumb Code    14  i2c.o(.text)
    I2CSlaveIntDisableEx                     0x00000ff1   Thumb Code    12  i2c.o(.text)
    I2CMasterIntStatus                       0x00000ffd   Thumb Code    28  i2c.o(.text)
    I2CMasterIntStatusEx                     0x00001019   Thumb Code    12  i2c.o(.text)
    I2CSlaveIntStatus                        0x00001025   Thumb Code    32  i2c.o(.text)
    I2CSlaveIntStatusEx                      0x00001045   Thumb Code    16  i2c.o(.text)
    I2CMasterIntClear                        0x00001055   Thumb Code     8  i2c.o(.text)
    I2CMasterIntClearEx                      0x0000105d   Thumb Code     4  i2c.o(.text)
    I2CSlaveIntClear                         0x00001061   Thumb Code     8  i2c.o(.text)
    I2CSlaveIntClearEx                       0x00001069   Thumb Code     6  i2c.o(.text)
    I2CMasterSlaveAddrSet                    0x0000106f   Thumb Code     8  i2c.o(.text)
    I2CMasterLineStateGet                    0x00001077   Thumb Code     6  i2c.o(.text)
    I2CMasterBusy                            0x0000107d   Thumb Code    18  i2c.o(.text)
    I2CMasterBusBusy                         0x0000108f   Thumb Code    18  i2c.o(.text)
    I2CMasterControl                         0x000010a1   Thumb Code     4  i2c.o(.text)
    I2CMasterErr                             0x000010a5   Thumb Code    30  i2c.o(.text)
    I2CMasterDataPut                         0x000010c3   Thumb Code     4  i2c.o(.text)
    I2CMasterDataGet                         0x000010c7   Thumb Code     6  i2c.o(.text)
    I2CMasterTimeoutSet                      0x000010cd   Thumb Code     4  i2c.o(.text)
    I2CSlaveACKOverride                      0x000010d1   Thumb Code    30  i2c.o(.text)
    I2CSlaveACKValueSet                      0x000010ef   Thumb Code    30  i2c.o(.text)
    I2CSlaveStatus                           0x0000110d   Thumb Code     8  i2c.o(.text)
    I2CSlaveDataPut                          0x00001115   Thumb Code     6  i2c.o(.text)
    I2CSlaveDataGet                          0x0000111b   Thumb Code     8  i2c.o(.text)
    I2CTxFIFOConfigSet                       0x00001123   Thumb Code    24  i2c.o(.text)
    I2CTxFIFOFlush                           0x0000113b   Thumb Code    14  i2c.o(.text)
    I2CRxFIFOConfigSet                       0x00001149   Thumb Code    22  i2c.o(.text)
    I2CRxFIFOFlush                           0x0000115f   Thumb Code    14  i2c.o(.text)
    I2CFIFOStatus                            0x0000116d   Thumb Code     8  i2c.o(.text)
    I2CFIFODataPut                           0x00001175   Thumb Code    20  i2c.o(.text)
    I2CFIFODataPutNonBlocking                0x00001189   Thumb Code    24  i2c.o(.text)
    I2CFIFODataGet                           0x000011a1   Thumb Code    22  i2c.o(.text)
    I2CFIFODataGetNonBlocking                0x000011b7   Thumb Code    26  i2c.o(.text)
    I2CMasterBurstLengthSet                  0x000011d1   Thumb Code     4  i2c.o(.text)
    I2CMasterBurstCountGet                   0x000011d5   Thumb Code     6  i2c.o(.text)
    I2CMasterGlitchFilterConfigSet           0x000011db   Thumb Code     8  i2c.o(.text)
    I2CSlaveFIFOEnable                       0x000011e3   Thumb Code    10  i2c.o(.text)
    I2CSlaveFIFODisable                      0x000011ed   Thumb Code     8  i2c.o(.text)
    IntMasterEnable                          0x00001219   Thumb Code    16  interrupt.o(.text)
    IntMasterDisable                         0x00001229   Thumb Code    16  interrupt.o(.text)
    IntRegister                              0x00001239   Thumb Code    52  interrupt.o(.text)
    IntUnregister                            0x0000126d   Thumb Code    10  interrupt.o(.text)
    IntPriorityGroupingSet                   0x00001277   Thumb Code    18  interrupt.o(.text)
    IntPriorityGroupingGet                   0x00001289   Thumb Code    36  interrupt.o(.text)
    IntPrioritySet                           0x000012ad   Thumb Code    46  interrupt.o(.text)
    IntPriorityGet                           0x000012db   Thumb Code    22  interrupt.o(.text)
    IntEnable                                0x000012f1   Thumb Code   120  interrupt.o(.text)
    IntDisable                               0x00001369   Thumb Code   120  interrupt.o(.text)
    IntIsEnabled                             0x000013e1   Thumb Code   106  interrupt.o(.text)
    IntPendSet                               0x0000144b   Thumb Code    98  interrupt.o(.text)
    IntPendClear                             0x000014ad   Thumb Code    76  interrupt.o(.text)
    IntPriorityMaskSet                       0x000014f9   Thumb Code    12  interrupt.o(.text)
    IntPriorityMaskGet                       0x00001505   Thumb Code     8  interrupt.o(.text)
    IntTrigger                               0x0000150d   Thumb Code    10  interrupt.o(.text)
    PWMGenConfigure                          0x00001545   Thumb Code    42  pwm.o(.text)
    PWMGenPeriodSet                          0x0000156f   Thumb Code    22  pwm.o(.text)
    PWMGenPeriodGet                          0x00001585   Thumb Code    24  pwm.o(.text)
    PWMGenEnable                             0x0000159d   Thumb Code    10  pwm.o(.text)
    PWMGenDisable                            0x000015a7   Thumb Code    10  pwm.o(.text)
    PWMPulseWidthSet                         0x000015b1   Thumb Code    38  pwm.o(.text)
    PWMPulseWidthGet                         0x000015d7   Thumb Code    40  pwm.o(.text)
    PWMDeadBandEnable                        0x000015ff   Thumb Code    18  pwm.o(.text)
    PWMDeadBandDisable                       0x00001611   Thumb Code    14  pwm.o(.text)
    PWMSyncUpdate                            0x0000161f   Thumb Code     4  pwm.o(.text)
    PWMSyncTimeBase                          0x00001623   Thumb Code     4  pwm.o(.text)
    PWMOutputState                           0x00001627   Thumb Code    20  pwm.o(.text)
    PWMOutputInvert                          0x0000163b   Thumb Code    20  pwm.o(.text)
    PWMOutputFaultLevel                      0x0000164f   Thumb Code    20  pwm.o(.text)
    PWMOutputFault                           0x00001663   Thumb Code    20  pwm.o(.text)
    PWMGenIntRegister                        0x0000176f   Thumb Code    38  pwm.o(.text)
    PWMGenIntUnregister                      0x00001795   Thumb Code    30  pwm.o(.text)
    PWMFaultIntRegister                      0x000017f1   Thumb Code    30  pwm.o(.text)
    PWMFaultIntUnregister                    0x0000180f   Thumb Code    26  pwm.o(.text)
    PWMGenIntTrigEnable                      0x00001829   Thumb Code    14  pwm.o(.text)
    PWMGenIntTrigDisable                     0x00001837   Thumb Code    14  pwm.o(.text)
    PWMGenIntStatus                          0x00001845   Thumb Code    16  pwm.o(.text)
    PWMGenIntClear                           0x00001855   Thumb Code     6  pwm.o(.text)
    PWMIntEnable                             0x0000185b   Thumb Code     8  pwm.o(.text)
    PWMIntDisable                            0x00001863   Thumb Code     8  pwm.o(.text)
    PWMFaultIntClear                         0x0000186b   Thumb Code     8  pwm.o(.text)
    PWMIntStatus                             0x00001873   Thumb Code    14  pwm.o(.text)
    PWMFaultIntClearExt                      0x00001881   Thumb Code     4  pwm.o(.text)
    PWMGenFaultConfigure                     0x00001885   Thumb Code    20  pwm.o(.text)
    PWMGenFaultTriggerSet                    0x00001899   Thumb Code    16  pwm.o(.text)
    PWMGenFaultTriggerGet                    0x000018a9   Thumb Code    16  pwm.o(.text)
    PWMGenFaultStatus                        0x000018b9   Thumb Code    38  pwm.o(.text)
    PWMGenFaultClear                         0x000018df   Thumb Code    36  pwm.o(.text)
    PWMClockSet                              0x00001903   Thumb Code    18  pwm.o(.text)
    PWMClockGet                              0x00001915   Thumb Code    60  pwm.o(.text)
    PWMOutputUpdateMode                      0x00001951   Thumb Code    82  pwm.o(.text)
    QEIEnable                                0x000019a5   Thumb Code    10  qei.o(.text)
    QEIDisable                               0x000019af   Thumb Code    10  qei.o(.text)
    QEIConfigure                             0x000019b9   Thumb Code    14  qei.o(.text)
    QEIPositionGet                           0x000019c7   Thumb Code     6  qei.o(.text)
    QEIPositionSet                           0x000019cd   Thumb Code     4  qei.o(.text)
    QEIDirectionGet                          0x000019d1   Thumb Code    20  qei.o(.text)
    QEIErrorGet                              0x000019e5   Thumb Code    10  qei.o(.text)
    QEIVelocityEnable                        0x000019ef   Thumb Code    10  qei.o(.text)
    QEIVelocityDisable                       0x000019f9   Thumb Code    10  qei.o(.text)
    QEIVelocityConfigure                     0x00001a03   Thumb Code    16  qei.o(.text)
    QEIVelocityGet                           0x00001a13   Thumb Code     6  qei.o(.text)
    QEIIntRegister                           0x00001a57   Thumb Code    30  qei.o(.text)
    QEIIntUnregister                         0x00001a75   Thumb Code    26  qei.o(.text)
    QEIIntEnable                             0x00001a8f   Thumb Code     8  qei.o(.text)
    QEIIntDisable                            0x00001a97   Thumb Code     8  qei.o(.text)
    QEIIntStatus                             0x00001a9f   Thumb Code    12  qei.o(.text)
    QEIIntClear                              0x00001aab   Thumb Code     4  qei.o(.text)
    SysCtlSRAMSizeGet                        0x00001b49   Thumb Code    12  sysctl.o(.text)
    SysCtlFlashSizeGet                       0x00001b55   Thumb Code    42  sysctl.o(.text)
    SysCtlFlashSectorSizeGet                 0x00001b7f   Thumb Code    40  sysctl.o(.text)
    SysCtlPeripheralPresent                  0x00001ba7   Thumb Code    52  sysctl.o(.text)
    SysCtlPeripheralReady                    0x00001bdb   Thumb Code    52  sysctl.o(.text)
    SysCtlPeripheralPowerOn                  0x00001c0f   Thumb Code    46  sysctl.o(.text)
    SysCtlPeripheralPowerOff                 0x00001c3d   Thumb Code    46  sysctl.o(.text)
    SysCtlPeripheralReset                    0x00001c6b   Thumb Code   106  sysctl.o(.text)
    SysCtlPeripheralEnable                   0x00001cd5   Thumb Code    46  sysctl.o(.text)
    SysCtlPeripheralDisable                  0x00001d03   Thumb Code    46  sysctl.o(.text)
    SysCtlPeripheralSleepEnable              0x00001d31   Thumb Code    46  sysctl.o(.text)
    SysCtlPeripheralSleepDisable             0x00001d5f   Thumb Code    46  sysctl.o(.text)
    SysCtlPeripheralDeepSleepEnable          0x00001d8d   Thumb Code    46  sysctl.o(.text)
    SysCtlPeripheralDeepSleepDisable         0x00001dbb   Thumb Code    46  sysctl.o(.text)
    SysCtlPeripheralClockGating              0x00001de9   Thumb Code    82  sysctl.o(.text)
    SysCtlIntRegister                        0x00001e3b   Thumb Code    20  sysctl.o(.text)
    SysCtlIntUnregister                      0x00001e4f   Thumb Code    16  sysctl.o(.text)
    SysCtlIntEnable                          0x00001e5f   Thumb Code    12  sysctl.o(.text)
    SysCtlIntDisable                         0x00001e6b   Thumb Code    12  sysctl.o(.text)
    SysCtlIntClear                           0x00001e77   Thumb Code     6  sysctl.o(.text)
    SysCtlIntStatus                          0x00001e7d   Thumb Code    16  sysctl.o(.text)
    SysCtlLDOSleepSet                        0x00001e8d   Thumb Code     6  sysctl.o(.text)
    SysCtlLDOSleepGet                        0x00001e93   Thumb Code     6  sysctl.o(.text)
    SysCtlLDODeepSleepSet                    0x00001e99   Thumb Code     8  sysctl.o(.text)
    SysCtlLDODeepSleepGet                    0x00001ea1   Thumb Code    60  sysctl.o(.text)
    SysCtlSleepPowerSet                      0x00001edd   Thumb Code     6  sysctl.o(.text)
    SysCtlDeepSleepPowerSet                  0x00001ee3   Thumb Code     8  sysctl.o(.text)
    SysCtlReset                              0x00001eeb   Thumb Code    10  sysctl.o(.text)
    SysCtlSleep                              0x00001ef5   Thumb Code     8  sysctl.o(.text)
    SysCtlDeepSleep                          0x00001efd   Thumb Code    40  sysctl.o(.text)
    SysCtlResetCauseGet                      0x00001f25   Thumb Code     6  sysctl.o(.text)
    SysCtlResetCauseClear                    0x00001f2b   Thumb Code    12  sysctl.o(.text)
    SysCtlMOSCConfigSet                      0x00001f37   Thumb Code     6  sysctl.o(.text)
    SysCtlPIOSCCalibrate                     0x00001f3d   Thumb Code    82  sysctl.o(.text)
    SysCtlResetBehaviorSet                   0x00001f8f   Thumb Code     8  sysctl.o(.text)
    SysCtlResetBehaviorGet                   0x00001f97   Thumb Code     8  sysctl.o(.text)
    SysCtlClockFreqSet                       0x00001f9f   Thumb Code   646  sysctl.o(.text)
    SysCtlClockSet                           0x00002225   Thumb Code   362  sysctl.o(.text)
    SysCtlClockGet                           0x0000238f   Thumb Code   244  sysctl.o(.text)
    SysCtlDeepSleepClockSet                  0x00002483   Thumb Code     8  sysctl.o(.text)
    SysCtlDeepSleepClockConfigSet            0x0000248b   Thumb Code   140  sysctl.o(.text)
    SysCtlPWMClockSet                        0x00002517   Thumb Code    16  sysctl.o(.text)
    SysCtlPWMClockGet                        0x00002527   Thumb Code    24  sysctl.o(.text)
    SysCtlADCSpeedSet                        0x0000253f   Thumb Code    36  sysctl.o(.text)
    SysCtlADCSpeedGet                        0x00002563   Thumb Code    12  sysctl.o(.text)
    SysCtlGPIOAHBEnable                      0x0000256f   Thumb Code    20  sysctl.o(.text)
    SysCtlGPIOAHBDisable                     0x00002583   Thumb Code    20  sysctl.o(.text)
    SysCtlUSBPLLEnable                       0x00002597   Thumb Code    14  sysctl.o(.text)
    SysCtlUSBPLLDisable                      0x000025a5   Thumb Code    14  sysctl.o(.text)
    SysCtlVoltageEventConfig                 0x000025b3   Thumb Code     6  sysctl.o(.text)
    SysCtlVoltageEventStatus                 0x000025b9   Thumb Code     6  sysctl.o(.text)
    SysCtlVoltageEventClear                  0x000025bf   Thumb Code    12  sysctl.o(.text)
    SysCtlNMIStatus                          0x000025cb   Thumb Code     6  sysctl.o(.text)
    SysCtlNMIClear                           0x000025d1   Thumb Code    12  sysctl.o(.text)
    SysCtlClockOutConfig                     0x000025dd   Thumb Code    14  sysctl.o(.text)
    SysCtlAltClkConfig                       0x000025eb   Thumb Code     8  sysctl.o(.text)
    SysTickEnable                            0x0000261d   Thumb Code    18  systick.o(.text)
    SysTickDisable                           0x0000262f   Thumb Code    18  systick.o(.text)
    SysTickIntRegister                       0x00002641   Thumb Code    30  systick.o(.text)
    SysTickIntUnregister                     0x0000265f   Thumb Code    26  systick.o(.text)
    SysTickIntEnable                         0x00002679   Thumb Code    18  systick.o(.text)
    SysTickIntDisable                        0x0000268b   Thumb Code    18  systick.o(.text)
    SysTickPeriodSet                         0x0000269d   Thumb Code    10  systick.o(.text)
    SysTickPeriodGet                         0x000026a7   Thumb Code    10  systick.o(.text)
    SysTickValueGet                          0x000026b1   Thumb Code     8  systick.o(.text)
    TimerEnable                              0x000026fd   Thumb Code    14  timer.o(.text)
    TimerDisable                             0x0000270b   Thumb Code    14  timer.o(.text)
    TimerConfigure                           0x00002719   Thumb Code    84  timer.o(.text)
    TimerControlLevel                        0x0000276d   Thumb Code    22  timer.o(.text)
    TimerControlTrigger                      0x00002783   Thumb Code    60  timer.o(.text)
    TimerControlEvent                        0x000027bf   Thumb Code    22  timer.o(.text)
    TimerControlStall                        0x000027d5   Thumb Code    22  timer.o(.text)
    TimerControlWaitOnTrigger                0x000027eb   Thumb Code    52  timer.o(.text)
    TimerRTCEnable                           0x0000281f   Thumb Code    10  timer.o(.text)
    TimerRTCDisable                          0x00002829   Thumb Code    10  timer.o(.text)
    TimerClockSourceSet                      0x00002833   Thumb Code     6  timer.o(.text)
    TimerClockSourceGet                      0x00002839   Thumb Code     8  timer.o(.text)
    TimerPrescaleSet                         0x00002841   Thumb Code    16  timer.o(.text)
    TimerPrescaleGet                         0x00002851   Thumb Code    14  timer.o(.text)
    TimerPrescaleMatchSet                    0x0000285f   Thumb Code    16  timer.o(.text)
    TimerPrescaleMatchGet                    0x0000286f   Thumb Code    14  timer.o(.text)
    TimerLoadSet                             0x0000287d   Thumb Code    16  timer.o(.text)
    TimerLoadGet                             0x0000288d   Thumb Code    14  timer.o(.text)
    TimerLoadSet64                           0x0000289b   Thumb Code     6  timer.o(.text)
    TimerLoadGet64                           0x000028a1   Thumb Code    26  timer.o(.text)
    TimerValueGet                            0x000028bb   Thumb Code    14  timer.o(.text)
    TimerValueGet64                          0x000028c9   Thumb Code    26  timer.o(.text)
    TimerMatchSet                            0x000028e3   Thumb Code    16  timer.o(.text)
    TimerMatchGet                            0x000028f3   Thumb Code    14  timer.o(.text)
    TimerMatchSet64                          0x00002901   Thumb Code     6  timer.o(.text)
    TimerMatchGet64                          0x00002907   Thumb Code    26  timer.o(.text)
    TimerIntRegister                         0x00002921   Thumb Code    38  timer.o(.text)
    TimerIntUnregister                       0x00002947   Thumb Code    30  timer.o(.text)
    TimerIntEnable                           0x00002965   Thumb Code     8  timer.o(.text)
    TimerIntDisable                          0x0000296d   Thumb Code     8  timer.o(.text)
    TimerIntStatus                           0x00002975   Thumb Code    12  timer.o(.text)
    TimerIntClear                            0x00002981   Thumb Code     4  timer.o(.text)
    TimerSynchronize                         0x00002985   Thumb Code     4  timer.o(.text)
    TimerADCEventSet                         0x00002989   Thumb Code     4  timer.o(.text)
    TimerADCEventGet                         0x0000298d   Thumb Code     6  timer.o(.text)
    TimerDMAEventSet                         0x00002993   Thumb Code     4  timer.o(.text)
    TimerDMAEventGet                         0x00002997   Thumb Code     6  timer.o(.text)
    UARTParityModeSet                        0x000029e9   Thumb Code    12  uart.o(.text)
    UARTParityModeGet                        0x000029f5   Thumb Code    10  uart.o(.text)
    UARTFIFOLevelSet                         0x000029ff   Thumb Code     8  uart.o(.text)
    UARTFIFOLevelGet                         0x00002a07   Thumb Code    18  uart.o(.text)
    UARTEnable                               0x00002a19   Thumb Code    20  uart.o(.text)
    UARTDisable                              0x00002a2d   Thumb Code    32  uart.o(.text)
    UARTConfigSetExpClk                      0x00002a4d   Thumb Code    76  uart.o(.text)
    UARTConfigGetExpClk                      0x00002a99   Thumb Code    44  uart.o(.text)
    UARTFIFOEnable                           0x00002ac5   Thumb Code    10  uart.o(.text)
    UARTFIFODisable                          0x00002acf   Thumb Code    10  uart.o(.text)
    UARTEnableSIR                            0x00002ad9   Thumb Code    22  uart.o(.text)
    UARTDisableSIR                           0x00002aef   Thumb Code    10  uart.o(.text)
    UARTSmartCardEnable                      0x00002af9   Thumb Code    22  uart.o(.text)
    UARTSmartCardDisable                     0x00002b0f   Thumb Code    10  uart.o(.text)
    UARTModemControlSet                      0x00002b19   Thumb Code    12  uart.o(.text)
    UARTModemControlClear                    0x00002b25   Thumb Code    12  uart.o(.text)
    UARTModemControlGet                      0x00002b31   Thumb Code    10  uart.o(.text)
    UARTModemStatusGet                       0x00002b3b   Thumb Code    12  uart.o(.text)
    UARTFlowControlSet                       0x00002b47   Thumb Code    12  uart.o(.text)
    UARTFlowControlGet                       0x00002b53   Thumb Code    10  uart.o(.text)
    UARTTxIntModeSet                         0x00002b5d   Thumb Code    12  uart.o(.text)
    UARTTxIntModeGet                         0x00002b69   Thumb Code    10  uart.o(.text)
    UARTCharsAvail                           0x00002b73   Thumb Code    12  uart.o(.text)
    UARTSpaceAvail                           0x00002b7f   Thumb Code    12  uart.o(.text)
    UARTCharGetNonBlocking                   0x00002b8b   Thumb Code    20  uart.o(.text)
    UARTCharGet                              0x00002b9f   Thumb Code    18  uart.o(.text)
    UARTCharPutNonBlocking                   0x00002bb1   Thumb Code    20  uart.o(.text)
    UARTCharPut                              0x00002bc5   Thumb Code    16  uart.o(.text)
    UARTBreakCtl                             0x00002bd5   Thumb Code    20  uart.o(.text)
    UARTBusy                                 0x00002be9   Thumb Code    10  uart.o(.text)
    UARTIntRegister                          0x00002bf3   Thumb Code    30  uart.o(.text)
    UARTIntUnregister                        0x00002c11   Thumb Code    26  uart.o(.text)
    UARTIntEnable                            0x00002c2b   Thumb Code     8  uart.o(.text)
    UARTIntDisable                           0x00002c33   Thumb Code     8  uart.o(.text)
    UARTIntStatus                            0x00002c3b   Thumb Code    12  uart.o(.text)
    UARTIntClear                             0x00002c47   Thumb Code     4  uart.o(.text)
    UARTDMAEnable                            0x00002c4b   Thumb Code     8  uart.o(.text)
    UARTDMADisable                           0x00002c53   Thumb Code     8  uart.o(.text)
    UARTRxErrorGet                           0x00002c5b   Thumb Code    10  uart.o(.text)
    UARTRxErrorClear                         0x00002c65   Thumb Code     6  uart.o(.text)
    UARTClockSourceSet                       0x00002c6b   Thumb Code     6  uart.o(.text)
    UARTClockSourceGet                       0x00002c71   Thumb Code     8  uart.o(.text)
    UART9BitEnable                           0x00002c79   Thumb Code    14  uart.o(.text)
    UART9BitDisable                          0x00002c87   Thumb Code    14  uart.o(.text)
    UART9BitAddrSet                          0x00002c95   Thumb Code    10  uart.o(.text)
    UART9BitAddrSend                         0x00002c9f   Thumb Code    44  uart.o(.text)
    uDMAEnable                               0x00002ce1   Thumb Code     8  udma.o(.text)
    uDMADisable                              0x00002ce9   Thumb Code     8  udma.o(.text)
    uDMAErrorStatusGet                       0x00002cf1   Thumb Code     6  udma.o(.text)
    uDMAErrorStatusClear                     0x00002cf7   Thumb Code     8  udma.o(.text)
    uDMAChannelEnable                        0x00002cff   Thumb Code    14  udma.o(.text)
    uDMAChannelDisable                       0x00002d0d   Thumb Code    14  udma.o(.text)
    uDMAChannelIsEnabled                     0x00002d1b   Thumb Code    26  udma.o(.text)
    uDMAControlBaseSet                       0x00002d35   Thumb Code     6  udma.o(.text)
    uDMAControlBaseGet                       0x00002d3b   Thumb Code     6  udma.o(.text)
    uDMAControlAlternateBaseGet              0x00002d41   Thumb Code     6  udma.o(.text)
    uDMAChannelRequest                       0x00002d47   Thumb Code    14  udma.o(.text)
    uDMAChannelAttributeEnable               0x00002d55   Thumb Code    62  udma.o(.text)
    uDMAChannelAttributeDisable              0x00002d93   Thumb Code    62  udma.o(.text)
    uDMAChannelAttributeGet                  0x00002dd1   Thumb Code    74  udma.o(.text)
    uDMAChannelControlSet                    0x00002e1b   Thumb Code    30  udma.o(.text)
    uDMAChannelTransferSet                   0x00002e39   Thumb Code   170  udma.o(.text)
    uDMAChannelScatterGatherSet              0x00002ee3   Thumb Code    82  udma.o(.text)
    uDMAChannelSizeGet                       0x00002f35   Thumb Code    38  udma.o(.text)
    uDMAChannelModeGet                       0x00002f5b   Thumb Code    42  udma.o(.text)
    uDMAChannelSelectSecondary               0x00002f85   Thumb Code    12  udma.o(.text)
    uDMAChannelSelectDefault                 0x00002f91   Thumb Code    12  udma.o(.text)
    uDMAIntRegister                          0x00002f9d   Thumb Code    22  udma.o(.text)
    uDMAIntUnregister                        0x00002fb3   Thumb Code    18  udma.o(.text)
    uDMAIntStatus                            0x00002fc5   Thumb Code     8  udma.o(.text)
    uDMAIntClear                             0x00002fcd   Thumb Code     8  udma.o(.text)
    uDMAChannelAssign                        0x00002fd5   Thumb Code    38  udma.o(.text)
    usart_send                               0x0000300d   Thumb Code   302  main.o(.text)
    pwm_proc                                 0x0000313b   Thumb Code    34  main.o(.text)
    main                                     0x0000315d   Thumb Code   196  main.o(.text)
    UARTStdioConfig                          0x00003251   Thumb Code    96  uartstdio.o(.text)
    UARTwrite                                0x000032b1   Thumb Code    58  uartstdio.o(.text)
    UARTgets                                 0x000032eb   Thumb Code   138  uartstdio.o(.text)
    UARTgetc                                 0x00003375   Thumb Code    20  uartstdio.o(.text)
    UARTvprintf                              0x00003389   Thumb Code   574  uartstdio.o(.text)
    UARTprintf                               0x000035c7   Thumb Code    26  uartstdio.o(.text)
    fputc                                    0x00003609   Thumb Code    18  uart_app.o(.text)
    fgetc                                    0x0000361b   Thumb Code    16  uart_app.o(.text)
    UART0_IRQHandler                         0x0000362b   Thumb Code   154  uart_app.o(.text)
    Uart_Proc                                0x000036c5   Thumb Code    52  uart_app.o(.text)
    ConfigureDMA                             0x000036f9   Thumb Code   108  uart_app.o(.text)
    ConfigureUART                            0x00003765   Thumb Code   150  uart_app.o(.text)
    TIMER0A_Handler                          0x000037fb   Thumb Code   342  uart_app.o(.text)
    Time_init                                0x00003951   Thumb Code    86  uart_app.o(.text)
    scheduler_init                           0x00003a05   Thumb Code     8  scheduler.o(.text)
    scheduler_run                            0x00003a0d   Thumb Code    76  scheduler.o(.text)
    Key_Init                                 0x00003a65   Thumb Code    84  key_app.o(.text)
    Key_Read                                 0x00003ab9   Thumb Code    32  key_app.o(.text)
    Key_Proc                                 0x00003ad9   Thumb Code   116  key_app.o(.text)
    Adc_Proc                                 0x00003b89   Thumb Code    10  adc_app.o(.text)
    ADC_Timer_Init                           0x00003b93   Thumb Code    70  adc_app.o(.text)
    ADC0_Handler                             0x00003bd9   Thumb Code   388  adc_app.o(.text)
    ADC_DMA_Init                             0x00003d5d   Thumb Code   300  adc_app.o(.text)
    Init_I2C                                 0x00003ed1   Thumb Code    80  myiic.o(.text)
    i2cWriteData                             0x00003f21   Thumb Code   124  myiic.o(.text)
    i2cRead                                  0x00003f9d   Thumb Code    84  myiic.o(.text)
    i2cWrite                                 0x00003ff1   Thumb Code    20  myiic.o(.text)
    i2cReadData                              0x00004005   Thumb Code   166  myiic.o(.text)
    Single_WriteI2C                          0x000040ab   Thumb Code    20  myiic.o(.text)
    Single_ReadI2C                           0x000040bf   Thumb Code    16  myiic.o(.text)
    Double_ReadI2C                           0x000040cf   Thumb Code    40  myiic.o(.text)
    Init_I2C0                                0x000040f7   Thumb Code    76  myiic.o(.text)
    i2c0WriteData                            0x00004143   Thumb Code   162  myiic.o(.text)
    i2c0Read                                 0x000041e5   Thumb Code   110  myiic.o(.text)
    i2c0Write                                0x00004253   Thumb Code    20  myiic.o(.text)
    i2c0ReadData                             0x00004267   Thumb Code   256  myiic.o(.text)
    IMU_Calibration                          0x0000436d   Thumb Code   154  icm20608.o(.text)
    ICM20608_Init                            0x00004407   Thumb Code   166  icm20608.o(.text)
    MPU6050_Read_Data                        0x000044ad   Thumb Code   284  icm20608.o(.text)
    initTime                                 0x000045f5   Thumb Code    34  systicktime.o(.text)
    micros                                   0x00004617   Thumb Code     6  systicktime.o(.text)
    delayMicroseconds                        0x0000461d   Thumb Code    24  systicktime.o(.text)
    delay                                    0x00004635   Thumb Code    18  systicktime.o(.text)
    millis                                   0x00004647   Thumb Code    14  systicktime.o(.text)
    Delay_Ms                                 0x00004655   Thumb Code    12  systicktime.o(.text)
    delay_ms                                 0x00004661   Thumb Code    12  systicktime.o(.text)
    delay_us                                 0x0000466d   Thumb Code    12  systicktime.o(.text)
    Delay_Us                                 0x00004679   Thumb Code    12  systicktime.o(.text)
    Test_Period                              0x00004685   Thumb Code    68  systicktime.o(.text)
    calculate_adaptive_alpha                 0x000046d9   Thumb Code    66  imu.o(.text)
    invSqrt                                  0x0000471b   Thumb Code    56  imu.o(.text)
    kalman_filter                            0x00004753   Thumb Code   128  imu.o(.text)
    imu_init                                 0x000047d3   Thumb Code    34  imu.o(.text)
    imu_update                               0x0000495b   Thumb Code  1410  imu.o(.text)
    ekf_update                               0x00004edd   Thumb Code   348  imu.o(.text)
    is_yaw_outlier                           0x00005039   Thumb Code    46  imu.o(.text)
    imu_get_euler_angles                     0x00005067   Thumb Code   430  imu.o(.text)
    OLED_GPIO_Init                           0x00005239   Thumb Code    60  oled.o(.text)
    SDA_OUT                                  0x00005275   Thumb Code    14  oled.o(.text)
    SDA_IN                                   0x00005283   Thumb Code    14  oled.o(.text)
    OLED_IIC_Start                           0x00005291   Thumb Code    56  oled.o(.text)
    OLED_IIC_Stop                            0x000052c9   Thumb Code    44  oled.o(.text)
    IIC_Wait_Ack                             0x000052f5   Thumb Code    80  oled.o(.text)
    IIC_Ack                                  0x00005345   Thumb Code    56  oled.o(.text)
    IIC_NAck                                 0x0000537d   Thumb Code    56  oled.o(.text)
    Write_IIC_Byte                           0x000053b5   Thumb Code    96  oled.o(.text)
    OLED_WrDat                               0x00005415   Thumb Code    32  oled.o(.text)
    OLED_WrCmd                               0x00005435   Thumb Code    32  oled.o(.text)
    OLED_Set_Pos                             0x00005455   Thumb Code    40  oled.o(.text)
    OLED_Fill                                0x0000547d   Thumb Code    58  oled.o(.text)
    OLED_CLS                                 0x000054b7   Thumb Code    56  oled.o(.text)
    OLED_Init_I2C                            0x000054ef   Thumb Code   186  oled.o(.text)
    LCD_WrDat                                0x000055a9   Thumb Code    92  oled.o(.text)
    LCD_WrCmd                                0x00005605   Thumb Code   110  oled.o(.text)
    LCD_Set_Pos                              0x00005673   Thumb Code    40  oled.o(.text)
    LCD_Fill                                 0x0000569b   Thumb Code    58  oled.o(.text)
    LCD_CLS                                  0x000056d5   Thumb Code    56  oled.o(.text)
    LCD_P6x8Str                              0x0000570d   Thumb Code    96  oled.o(.text)
    LCD_P6x8Char                             0x0000576d   Thumb Code    68  oled.o(.text)
    write_6_8_number                         0x000057b1   Thumb Code   696  oled.o(.text)
    LCD_P8x16Str                             0x00005a69   Thumb Code   130  oled.o(.text)
    LCD_P8x16Char                            0x00005aeb   Thumb Code   154  oled.o(.text)
    write_8_16_number                        0x00005b85   Thumb Code   378  oled.o(.text)
    write_16_16_CN                           0x00005cff   Thumb Code    64  oled.o(.text)
    LCD_clear_L                              0x00005d3f   Thumb Code    54  oled.o(.text)
    Draw_Logo                                0x00005d75   Thumb Code    62  oled.o(.text)
    OLEDInit                                 0x00005db3   Thumb Code   216  oled.o(.text)
    OLED_Init                                0x00005e8b   Thumb Code   100  oled.o(.text)
    OLED_Init_Fast                           0x00005eef   Thumb Code   106  oled.o(.text)
    display_6_8_number                       0x00005f59   Thumb Code    32  oled.o(.text)
    display_6_8_string                       0x00005f79   Thumb Code    20  oled.o(.text)
    ssd1306_width                            0x00005f8d   Thumb Code     8  ssd1306.o(.text)
    ssd1306_height                           0x00005f95   Thumb Code     8  ssd1306.o(.text)
    set_rotation                             0x00005f9d   Thumb Code    80  ssd1306.o(.text)
    ssd1306_command                          0x00005fed   Thumb Code    12  ssd1306.o(.text)
    ssd1306_begin                            0x00005ff9   Thumb Code   294  ssd1306.o(.text)
    ssd1306_draw_pixel                       0x0000611f   Thumb Code   268  ssd1306.o(.text)
    ssd1306_invert_display                   0x0000622b   Thumb Code    22  ssd1306.o(.text)
    ssd1306_start_scroll_right               0x00006241   Thumb Code    56  ssd1306.o(.text)
    ssd1306_start_scroll_left                0x00006279   Thumb Code    56  ssd1306.o(.text)
    ssd1306_start_scroll_diag_right          0x000062b1   Thumb Code    68  ssd1306.o(.text)
    ssd1306_start_scroll_diag_left           0x000062f5   Thumb Code    68  ssd1306.o(.text)
    ssd1306_stop_scroll                      0x00006339   Thumb Code    10  ssd1306.o(.text)
    ssd1306_dim                              0x00006343   Thumb Code    94  ssd1306.o(.text)
    ssd1306_data                             0x000063a1   Thumb Code    12  ssd1306.o(.text)
    draw_oled                                0x000063ad   Thumb Code    50  ssd1306.o(.text)
    ssd1306_display                          0x000063df   Thumb Code    44  ssd1306.o(.text)
    ssd1306_clear_display                    0x0000640b   Thumb Code    14  ssd1306.o(.text)
    ssd1306_draw_fast_hline_internal         0x00006419   Thumb Code   174  ssd1306.o(.text)
    ssd1306_draw_fast_vline_internal         0x000064c7   Thumb Code   378  ssd1306.o(.text)
    ssd1306_draw_fast_hline                  0x00006641   Thumb Code   154  ssd1306.o(.text)
    ssd1306_draw_fast_vline                  0x000066db   Thumb Code   154  ssd1306.o(.text)
    ssd1306_draw_circle                      0x00006775   Thumb Code   286  ssd1306.o(.text)
    ssd1306_draw_circle_helper               0x00006893   Thumb Code   230  ssd1306.o(.text)
    ssd1306_fill_circle_helper               0x00006979   Thumb Code   198  ssd1306.o(.text)
    ssd1306_fill_circle                      0x00006a3f   Thumb Code    48  ssd1306.o(.text)
    ssd1306_draw_line                        0x00006a6f   Thumb Code   206  ssd1306.o(.text)
    ssd1306_draw_rect                        0x00006b3d   Thumb Code    76  ssd1306.o(.text)
    ssd1306_fill_rect                        0x00006b89   Thumb Code    46  ssd1306.o(.text)
    ssd1306_fill_screen                      0x00006bb7   Thumb Code    28  ssd1306.o(.text)
    ssd1306_draw_round_rect                  0x00006bd3   Thumb Code   210  ssd1306.o(.text)
    ssd1306_fill_round_rect                  0x00006ca5   Thumb Code   108  ssd1306.o(.text)
    ssd1306_draw_triangle                    0x00006d11   Thumb Code    64  ssd1306.o(.text)
    ssd1306_fill_triangle                    0x00006d51   Thumb Code   400  ssd1306.o(.text)
    ssd1306_draw_bitmap                      0x00006ee1   Thumb Code   100  ssd1306.o(.text)
    ssd1306_draw_bitmap_bg                   0x00006f45   Thumb Code   126  ssd1306.o(.text)
    ssd1306_draw_xbitmap                     0x00006fc3   Thumb Code   110  ssd1306.o(.text)
    ssd1306_draw_char                        0x00007031   Thumb Code   250  ssd1306.o(.text)
    ssd1306_write                            0x0000712b   Thumb Code   156  ssd1306.o(.text)
    ssd1306_set_cursor                       0x000071c7   Thumb Code    10  ssd1306.o(.text)
    ssd1306_get_cursor_x                     0x000071d1   Thumb Code     8  ssd1306.o(.text)
    ssd1306_get_cursor_y                     0x000071d9   Thumb Code     8  ssd1306.o(.text)
    ssd1306_set_textsize                     0x000071e1   Thumb Code    16  ssd1306.o(.text)
    ssd1306_set_textcolor                    0x000071f1   Thumb Code    10  ssd1306.o(.text)
    ssd1306_set_textcolor_bg                 0x000071fb   Thumb Code    10  ssd1306.o(.text)
    ssd1306_set_textwrap                     0x00007205   Thumb Code    14  ssd1306.o(.text)
    ssd1306_get_rotation                     0x00007213   Thumb Code     6  ssd1306.o(.text)
    ssd1306_set_rotation                     0x00007219   Thumb Code    80  ssd1306.o(.text)
    ssd1306_cp437                            0x00007269   Thumb Code    14  ssd1306.o(.text)
    ssd1306_putstring                        0x00007277   Thumb Code    22  ssd1306.o(.text)
    ssd1306_puts                             0x0000728d   Thumb Code    18  ssd1306.o(.text)
    Init                                     0x000072d5   Thumb Code    26  oled_app.o(.text)
    LCD_DisplayStringLine                    0x000072ef   Thumb Code    34  oled_app.o(.text)
    LcdSprintf                               0x00007311   Thumb Code    42  oled_app.o(.text)
    Oled_Proc                                0x0000733b   Thumb Code    26  oled_app.o(.text)
    QEIApp_UnlockPF0                         0x00007369   Thumb Code    26  qei_app.o(.text)
    QEIApp_Init                              0x00007433   Thumb Code   392  qei_app.o(.text)
    QEIApp_GetLeftSignedVelocity             0x000075bb   Thumb Code     6  qei_app.o(.text)
    QEIApp_GetRightSignedVelocity            0x000075c1   Thumb Code     6  qei_app.o(.text)
    QEIApp_GetLeftRPM                        0x000075c7   Thumb Code     8  qei_app.o(.text)
    QEIApp_GetRightRPM                       0x000075cf   Thumb Code     8  qei_app.o(.text)
    QEIApp_GetData                           0x000075d7   Thumb Code     4  qei_app.o(.text)
    PWM_APP_GetChannelConfig                 0x00007619   Thumb Code   192  pwm_app.o(.text)
    PWM_APP_Init                             0x000076d9   Thumb Code   568  pwm_app.o(.text)
    PWM_APP_SetPulseWidth                    0x00007911   Thumb Code    38  pwm_app.o(.text)
    PWM_APP_SetDuty                          0x00007937   Thumb Code    64  pwm_app.o(.text)
    PWM_APP_SetServo                         0x00007977   Thumb Code    74  pwm_app.o(.text)
    PWM_APP_OutputMulti                      0x000079c1   Thumb Code   196  pwm_app.o(.text)
    pwm_debug_init                           0x00007ab7   Thumb Code    28  pwm_debug.o(.text)
    pwm_debug_get_duty                       0x00007ad3   Thumb Code    60  pwm_debug.o(.text)
    pwm_debug_get_level                      0x00007b0f   Thumb Code   134  pwm_debug.o(.text)
    pwm_debug_report                         0x00007b95   Thumb Code   110  pwm_debug.o(.text)
    pwm_debug_report_multi                   0x00007c03   Thumb Code   276  pwm_debug.o(.text)
    dual_motor_init                          0x00007d79   Thumb Code    78  motor_app.o(.text)
    left_motor_set_speed                     0x00007dc7   Thumb Code   110  motor_app.o(.text)
    right_motor_set_speed                    0x00007e35   Thumb Code   110  motor_app.o(.text)
    my_right_motor_set_speed                 0x00007ea3   Thumb Code   130  motor_app.o(.text)
    my_left_motor_set_speed                  0x00007f25   Thumb Code   130  motor_app.o(.text)
    car_forward                              0x00007fa7   Thumb Code    46  motor_app.o(.text)
    car_backward                             0x00007fd5   Thumb Code    46  motor_app.o(.text)
    car_turn_left                            0x00008003   Thumb Code    58  motor_app.o(.text)
    car_turn_right                           0x0000803d   Thumb Code    58  motor_app.o(.text)
    car_spin_left                            0x00008077   Thumb Code    46  motor_app.o(.text)
    car_spin_right                           0x000080a5   Thumb Code    46  motor_app.o(.text)
    car_stop                                 0x000080d3   Thumb Code    44  motor_app.o(.text)
    pid_init                                 0x00008109   Thumb Code    78  pid.o(.text)
    pid_set_target                           0x00008157   Thumb Code     6  pid.o(.text)
    pid_set_params                           0x0000815d   Thumb Code    14  pid.o(.text)
    pid_set_limit                            0x0000816b   Thumb Code     6  pid.o(.text)
    pid_reset                                0x00008171   Thumb Code    58  pid.o(.text)
    pid_calculate_positional                 0x00008265   Thumb Code    38  pid.o(.text)
    pid_calculate_incremental                0x00008319   Thumb Code    38  pid.o(.text)
    pid_app_init                             0x00008397   Thumb Code    98  pid_app.o(.text)
    pid_app_set_target_speed                 0x000083f9   Thumb Code    56  pid_app.o(.text)
    pid_app_update_speed                     0x00008431   Thumb Code    38  pid_app.o(.text)
    pid_app_calc                             0x00008457   Thumb Code   136  pid_app.o(.text)
    pid_app_task                             0x000084df   Thumb Code    14  pid_app.o(.text)
    pid_app_start                            0x000084ed   Thumb Code    32  pid_app.o(.text)
    pid_app_stop                             0x0000850d   Thumb Code    24  pid_app.o(.text)
    pid_app_set_left_params                  0x00008525   Thumb Code    64  pid_app.o(.text)
    pid_app_set_right_params                 0x00008565   Thumb Code    64  pid_app.o(.text)
    __aeabi_memcpy                           0x000085cd   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x000085cd   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x000085cd   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x000085f1   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x000085f1   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x000085f1   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x000085ff   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x000085ff   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x000085ff   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x00008603   Thumb Code    18  memseta.o(.text)
    __aeabi_dadd                             0x00008615   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x00008757   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x0000875d   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x00008763   Thumb Code   228  dmul.o(.text)
    __aeabi_f2d                              0x00008847   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x0000886d   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x000088a5   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x000088a5   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x000088d1   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x00008933   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x00008933   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x00008951   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x00008951   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x00008975   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x00008975   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x00008987   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x000089e3   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x00008a01   Thumb Code   156  depilogue.o(.text)
    __aeabi_ddiv                             0x00008a9d   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x00008b7b   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x00008bad   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x00008bdd   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x00008bdd   Thumb Code     0  init.o(.text)
    __aeabi_llsr                             0x00008c01   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x00008c01   Thumb Code     0  llushr.o(.text)
    __decompress                             0x00008c21   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x00008c21   Thumb Code    86  __dczerorl2.o(.text)
    __0printf                                0x00008c79   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x00008c79   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x00008c79   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x00008c79   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x00008c79   Thumb Code     0  printfa.o(i.__0printf)
    __0snprintf                              0x00008c99   Thumb Code    48  printfa.o(i.__0snprintf)
    __1snprintf                              0x00008c99   Thumb Code     0  printfa.o(i.__0snprintf)
    __2snprintf                              0x00008c99   Thumb Code     0  printfa.o(i.__0snprintf)
    __c89snprintf                            0x00008c99   Thumb Code     0  printfa.o(i.__0snprintf)
    snprintf                                 0x00008c99   Thumb Code     0  printfa.o(i.__0snprintf)
    __0vsprintf                              0x00008ccd   Thumb Code    30  printfa.o(i.__0vsprintf)
    __1vsprintf                              0x00008ccd   Thumb Code     0  printfa.o(i.__0vsprintf)
    __2vsprintf                              0x00008ccd   Thumb Code     0  printfa.o(i.__0vsprintf)
    __c89vsprintf                            0x00008ccd   Thumb Code     0  printfa.o(i.__0vsprintf)
    vsprintf                                 0x00008ccd   Thumb Code     0  printfa.o(i.__0vsprintf)
    __ARM_fpclassifyf                        0x00008cf1   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_asinf                           0x00008d19   Thumb Code   258  asinf.o(i.__hardfp_asinf)
    __hardfp_atan2f                          0x00008e45   Thumb Code   594  atan2f.o(i.__hardfp_atan2f)
    __hardfp_cosf                            0x000090f1   Thumb Code   280  cosf.o(i.__hardfp_cosf)
    __hardfp_sinf                            0x00009241   Thumb Code   344  sinf.o(i.__hardfp_sinf)
    __hardfp_sqrtf                           0x000093d1   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_infnan                     0x0000940b   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_infnan2                    0x00009411   Thumb Code     6  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_invalid                    0x00009419   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x00009429   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x00009439   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    __scatterload_copy                       0x0000958d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0000959b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0000959d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x000095ad   Thumb Code     6  errno.o(i.__set_errno)
    sqrtf                                    0x00009e63   Thumb Code    62  sqrtf.o(i.sqrtf)
    font                                     0x0000a544   Data        1275  glcdfont.o(.constdata)
    F6x8                                     0x0000aa3f   Data         552  oled.o(.constdata)
    F8X16                                    0x0000ac67   Data        1520  oled.o(.constdata)
    LOGO128x64                               0x0000b257   Data        1024  oled.o(.constdata)
    Yi_TC                                    0x0000b657   Data        1024  oled.o(.constdata)
    Yu_TC                                    0x0000ba57   Data        1024  oled.o(.constdata)
    Hzk32                                    0x0000be57   Data         640  oled.o(.constdata)
    asc2_1608                                0x0000c0d7   Data        1520  oled.o(.constdata)
    NC_Logo                                  0x0000c6c7   Data        1024  oled.o(.constdata)
    Hzk16                                    0x0000cac7   Data         224  oled.o(.constdata)
    Region$$Table$$Base                      0x0000cbf8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0000cc18   Number         0  anon$$obj.o(Region$$Table)
    pwm_config1                              0x20000008   Data          12  main.o(.data)
    pwm_config2                              0x20000014   Data          12  main.o(.data)
    configs                                  0x20000020   Data           8  main.o(.data)
    system_counter                           0x20000028   Data           8  main.o(.data)
    controlTable                             0x20000038   Data           4  uart_app.o(.data)
    rxBuffer0                                0x2000003c   Data           1  uart_app.o(.data)
    rxBuffer1                                0x2000003d   Data           1  uart_app.o(.data)
    systick                                  0x20000040   Data           4  uart_app.o(.data)
    uart_rx_ticks                            0x20000044   Data           4  uart_app.o(.data)
    uart_rx_index                            0x20000048   Data           1  uart_app.o(.data)
    timer10ms                                0x20000049   Data           1  uart_app.o(.data)
    task_num                                 0x2000004c   Data           1  scheduler.o(.data)
    Key_Down                                 0x20000068   Data           1  key_app.o(.data)
    Key_Up                                   0x20000069   Data           1  key_app.o(.data)
    Key_Val                                  0x2000006a   Data           1  key_app.o(.data)
    Key_Old                                  0x2000006b   Data           1  key_app.o(.data)
    Battery_Voltage                          0x2000006c   Data           4  adc_app.o(.data)
    IMU_ID                                   0x20000070   Data           1  icm20608.o(.data)
    g_Gyro_xoffset                           0x20000074   Data           4  icm20608.o(.data)
    g_Gyro_yoffset                           0x20000078   Data           4  icm20608.o(.data)
    g_Gyro_zoffset                           0x2000007c   Data           4  icm20608.o(.data)
    wrap                                     0x200000fc   Data           1  ssd1306.o(.data)
    _cp437                                   0x200000fd   Data           1  ssd1306.o(.data)
    pid_params_left                          0x20000510   Data          36  pid_app.o(.data)
    pid_params_right                         0x20000534   Data          36  pid_app.o(.data)
    target_left_rpm                          0x20000558   Data           4  pid_app.o(.data)
    target_right_rpm                         0x2000055c   Data           4  pid_app.o(.data)
    current_left_rpm                         0x20000560   Data           4  pid_app.o(.data)
    current_right_rpm                        0x20000564   Data           4  pid_app.o(.data)
    motor_left_speed                         0x20000569   Data           1  pid_app.o(.data)
    motor_right_speed                        0x2000056a   Data           1  pid_app.o(.data)
    motor_left_dir                           0x2000056b   Data           1  pid_app.o(.data)
    motor_right_dir                          0x2000056c   Data           1  pid_app.o(.data)
    __stdout                                 0x20000570   Data           4  stdout.o(.data)
    controlTableSpace                        0x20000c00   Data        1024  uart_app.o(.bss)
    uart_rx_buffer                           0x20001000   Data         128  uart_app.o(.bss)
    WP_Sensor                                0x20001080   Data         136  uart_app.o(.bss)
    adcBuffer0                               0x20001108   Data          20  adc_app.o(.bss)
    adcBuffer1                               0x2000111c   Data          20  adc_app.o(.bss)
    ADC_Values                               0x20001130   Data          20  adc_app.o(.bss)
    ADC_Filters                              0x20001148   Data          40  adc_app.o(.bss)
    gyro_offset                              0x20001170   Data          12  icm20608.o(.bss)
    pid_left                                 0x200011c0   Data          60  pid_app.o(.bss)
    pid_right                                0x200011fc   Data          60  pid_app.o(.bss)
    __initial_sp                             0x20001438   Data           0  startup_rvmdk.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x00000289

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x0000d684, Max: 0x00040000, ABSOLUTE, COMPRESSED[0x0000ce38])

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x0000cc18, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x00000288   Code   RO          734    RESET               startup_rvmdk.o
    0x00000288   0x00000288   0x00000000   Code   RO         1099  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x00000288   0x00000288   0x00000004   Code   RO         1396    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0000028c   0x0000028c   0x00000004   Code   RO         1399    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x00000290   0x00000290   0x00000000   Code   RO         1401    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x00000290   0x00000290   0x00000000   Code   RO         1403    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x00000290   0x00000290   0x00000008   Code   RO         1404    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x00000298   0x00000298   0x00000004   Code   RO         1411    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0000029c   0x0000029c   0x00000000   Code   RO         1406    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0000029c   0x0000029c   0x00000000   Code   RO         1408    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0000029c   0x0000029c   0x00000004   Code   RO         1397    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x000002a0   0x000002a0   0x00000026   Code   RO          109    .emb_text           cpu.o
    0x000002c6   0x000002c6   0x00000002   PAD
    0x000002c8   0x000002c8   0x00000006   Code   RO          481    .emb_text           sysctl.o
    0x000002ce   0x000002ce   0x00000002   PAD
    0x000002d0   0x000002d0   0x000003c4   Code   RO            1    .text               adc.o
    0x00000694   0x00000694   0x000007f8   Code   RO          284    .text               gpio.o
    0x00000e8c   0x00000e8c   0x00000388   Code   RO          325    .text               i2c.o
    0x00001214   0x00001214   0x00000330   Code   RO          345    .text               interrupt.o
    0x00001544   0x00001544   0x0000045e   Code   RO          393    .text               pwm.o
    0x000019a2   0x000019a2   0x00000002   PAD
    0x000019a4   0x000019a4   0x00000120   Code   RO          411    .text               qei.o
    0x00001ac4   0x00001ac4   0x00000b58   Code   RO          482    .text               sysctl.o
    0x0000261c   0x0000261c   0x0000009c   Code   RO          514    .text               systick.o
    0x000026b8   0x000026b8   0x000002f8   Code   RO          529    .text               timer.o
    0x000029b0   0x000029b0   0x00000330   Code   RO          549    .text               uart.o
    0x00002ce0   0x00002ce0   0x0000032c   Code   RO          569    .text               udma.o
    0x0000300c   0x0000300c   0x00000244   Code   RO          623    .text               main.o
    0x00003250   0x00003250   0x000003b8   Code   RO          713    .text               uartstdio.o
    0x00003608   0x00003608   0x000003fc   Code   RO          738    .text               uart_app.o
    0x00003a04   0x00003a04   0x00000060   Code   RO          760    .text               scheduler.o
    0x00003a64   0x00003a64   0x00000124   Code   RO          779    .text               key_app.o
    0x00003b88   0x00003b88   0x00000348   Code   RO          794    .text               adc_app.o
    0x00003ed0   0x00003ed0   0x0000049c   Code   RO          811    .text               myiic.o
    0x0000436c   0x0000436c   0x0000027c   Code   RO          826    .text               icm20608.o
    0x000045e8   0x000045e8   0x000000f0   Code   RO          845    .text               systicktime.o
    0x000046d8   0x000046d8   0x00000b60   Code   RO          862    .text               imu.o
    0x00005238   0x00005238   0x00000d54   Code   RO          893    .text               oled.o
    0x00005f8c   0x00005f8c   0x00001348   Code   RO          918    .text               ssd1306.o
    0x000072d4   0x000072d4   0x00000094   Code   RO          938    .text               oled_app.o
    0x00007368   0x00007368   0x000002b0   Code   RO          950    .text               qei_app.o
    0x00007618   0x00007618   0x0000046c   Code   RO          968    .text               pwm_app.o
    0x00007a84   0x00007a84   0x000002f4   Code   RO          980    .text               pwm_debug.o
    0x00007d78   0x00007d78   0x00000390   Code   RO          996    .text               motor_app.o
    0x00008108   0x00008108   0x0000023c   Code   RO         1011    .text               pid.o
    0x00008344   0x00008344   0x00000288   Code   RO         1023    .text               pid_app.o
    0x000085cc   0x000085cc   0x00000024   Code   RO         1102    .text               mc_w.l(memcpya.o)
    0x000085f0   0x000085f0   0x00000024   Code   RO         1104    .text               mc_w.l(memseta.o)
    0x00008614   0x00008614   0x0000014e   Code   RO         1369    .text               mf_w.l(dadd.o)
    0x00008762   0x00008762   0x000000e4   Code   RO         1371    .text               mf_w.l(dmul.o)
    0x00008846   0x00008846   0x00000026   Code   RO         1373    .text               mf_w.l(f2d.o)
    0x0000886c   0x0000886c   0x00000038   Code   RO         1375    .text               mf_w.l(d2f.o)
    0x000088a4   0x000088a4   0x0000002c   Code   RO         1413    .text               mc_w.l(uidiv.o)
    0x000088d0   0x000088d0   0x00000062   Code   RO         1415    .text               mc_w.l(uldiv.o)
    0x00008932   0x00008932   0x0000001e   Code   RO         1417    .text               mc_w.l(llshl.o)
    0x00008950   0x00008950   0x00000024   Code   RO         1419    .text               mc_w.l(llsshr.o)
    0x00008974   0x00008974   0x00000000   Code   RO         1428    .text               mc_w.l(iusefp.o)
    0x00008974   0x00008974   0x0000006e   Code   RO         1429    .text               mf_w.l(fepilogue.o)
    0x000089e2   0x000089e2   0x000000ba   Code   RO         1431    .text               mf_w.l(depilogue.o)
    0x00008a9c   0x00008a9c   0x000000de   Code   RO         1433    .text               mf_w.l(ddiv.o)
    0x00008b7a   0x00008b7a   0x00000030   Code   RO         1435    .text               mf_w.l(dfixul.o)
    0x00008baa   0x00008baa   0x00000002   PAD
    0x00008bac   0x00008bac   0x00000030   Code   RO         1437    .text               mf_w.l(cdrcmple.o)
    0x00008bdc   0x00008bdc   0x00000024   Code   RO         1439    .text               mc_w.l(init.o)
    0x00008c00   0x00008c00   0x00000020   Code   RO         1441    .text               mc_w.l(llushr.o)
    0x00008c20   0x00008c20   0x00000056   Code   RO         1451    .text               mc_w.l(__dczerorl2.o)
    0x00008c76   0x00008c76   0x00000002   PAD
    0x00008c78   0x00008c78   0x00000020   Code   RO         1341    i.__0printf         mc_w.l(printfa.o)
    0x00008c98   0x00008c98   0x00000034   Code   RO         1342    i.__0snprintf       mc_w.l(printfa.o)
    0x00008ccc   0x00008ccc   0x00000024   Code   RO         1347    i.__0vsprintf       mc_w.l(printfa.o)
    0x00008cf0   0x00008cf0   0x00000026   Code   RO         1377    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x00008d16   0x00008d16   0x00000002   PAD
    0x00008d18   0x00008d18   0x0000012c   Code   RO         1039    i.__hardfp_asinf    m_wm.l(asinf.o)
    0x00008e44   0x00008e44   0x000002ac   Code   RO         1051    i.__hardfp_atan2f   m_wm.l(atan2f.o)
    0x000090f0   0x000090f0   0x00000150   Code   RO         1063    i.__hardfp_cosf     m_wm.l(cosf.o)
    0x00009240   0x00009240   0x00000190   Code   RO         1075    i.__hardfp_sinf     m_wm.l(sinf.o)
    0x000093d0   0x000093d0   0x0000003a   Code   RO         1087    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x0000940a   0x0000940a   0x00000006   Code   RO         1380    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x00009410   0x00009410   0x00000006   Code   RO         1381    i.__mathlib_flt_infnan2  m_wm.l(funder.o)
    0x00009416   0x00009416   0x00000002   PAD
    0x00009418   0x00009418   0x00000010   Code   RO         1382    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x00009428   0x00009428   0x00000010   Code   RO         1385    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x00009438   0x00009438   0x00000154   Code   RO         1393    i.__mathlib_rredf2  m_wm.l(rredf.o)
    0x0000958c   0x0000958c   0x0000000e   Code   RO         1445    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0000959a   0x0000959a   0x00000002   Code   RO         1446    i.__scatterload_null  mc_w.l(handlers.o)
    0x0000959c   0x0000959c   0x0000000e   Code   RO         1447    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x000095aa   0x000095aa   0x00000002   PAD
    0x000095ac   0x000095ac   0x0000000c   Code   RO         1423    i.__set_errno       mc_w.l(errno.o)
    0x000095b8   0x000095b8   0x00000184   Code   RO         1348    i._fp_digits        mc_w.l(printfa.o)
    0x0000973c   0x0000973c   0x000006b4   Code   RO         1349    i._printf_core      mc_w.l(printfa.o)
    0x00009df0   0x00009df0   0x00000024   Code   RO         1350    i._printf_post_padding  mc_w.l(printfa.o)
    0x00009e14   0x00009e14   0x0000002e   Code   RO         1351    i._printf_pre_padding  mc_w.l(printfa.o)
    0x00009e42   0x00009e42   0x00000016   Code   RO         1352    i._snputc           mc_w.l(printfa.o)
    0x00009e58   0x00009e58   0x0000000a   Code   RO         1353    i._sputc            mc_w.l(printfa.o)
    0x00009e62   0x00009e62   0x0000003e   Code   RO         1089    i.sqrtf             m_wm.l(sqrtf.o)
    0x00009ea0   0x00009ea0   0x00000210   Data   RO          285    .constdata          gpio.o
    0x0000a0b0   0x0000a0b0   0x00000080   Data   RO          326    .constdata          i2c.o
    0x0000a130   0x0000a130   0x0000010c   Data   RO          346    .constdata          interrupt.o
    0x0000a23c   0x0000a23c   0x000001cc   Data   RO          483    .constdata          sysctl.o
    0x0000a408   0x0000a408   0x000000a0   Data   RO          530    .constdata          timer.o
    0x0000a4a8   0x0000a4a8   0x00000080   Data   RO          550    .constdata          uart.o
    0x0000a528   0x0000a528   0x0000001c   Data   RO          714    .constdata          uartstdio.o
    0x0000a544   0x0000a544   0x000004fb   Data   RO          884    .constdata          glcdfont.o
    0x0000aa3f   0x0000aa3f   0x00002168   Data   RO          894    .constdata          oled.o
    0x0000cba7   0x0000cba7   0x00000001   PAD
    0x0000cba8   0x0000cba8   0x00000010   Data   RO          997    .constdata          motor_app.o
    0x0000cbb8   0x0000cbb8   0x00000020   Data   RO         1394    .constdata          m_wm.l(rredf.o)
    0x0000cbd8   0x0000cbd8   0x0000000a   Data   RO          624    .conststring        main.o
    0x0000cbe2   0x0000cbe2   0x00000002   PAD
    0x0000cbe4   0x0000cbe4   0x00000011   Data   RO          715    .conststring        uartstdio.o
    0x0000cbf5   0x0000cbf5   0x00000003   PAD
    0x0000cbf8   0x0000cbf8   0x00000020   Data   RO         1443    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0000cc18, Size: 0x00001438, Max: 0x00008000, ABSOLUTE, COMPRESSED[0x00000220])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000003   Data   RW            2    .data               adc.o
    0x20000003   COMPRESSED   0x00000005   PAD
    0x20000008   COMPRESSED   0x00000028   Data   RW          625    .data               main.o
    0x20000030   COMPRESSED   0x00000005   Data   RW          716    .data               uartstdio.o
    0x20000035   COMPRESSED   0x00000003   PAD
    0x20000038   COMPRESSED   0x00000012   Data   RW          740    .data               uart_app.o
    0x2000004a   COMPRESSED   0x00000002   PAD
    0x2000004c   COMPRESSED   0x0000001c   Data   RW          761    .data               scheduler.o
    0x20000068   COMPRESSED   0x00000004   Data   RW          780    .data               key_app.o
    0x2000006c   COMPRESSED   0x00000004   Data   RW          796    .data               adc_app.o
    0x20000070   COMPRESSED   0x00000010   Data   RW          828    .data               icm20608.o
    0x20000080   COMPRESSED   0x00000004   Data   RW          846    .data               systicktime.o
    0x20000084   COMPRESSED   0x00000064   Data   RW          864    .data               imu.o
    0x200000e8   COMPRESSED   0x00000427   Data   RW          919    .data               ssd1306.o
    0x2000050f   COMPRESSED   0x00000001   Data   RW          998    .data               motor_app.o
    0x20000510   COMPRESSED   0x0000005d   Data   RW         1025    .data               pid_app.o
    0x2000056d   COMPRESSED   0x00000003   PAD
    0x20000570   COMPRESSED   0x00000004   Data   RW         1412    .data               mc_w.l(stdout.o)
    0x20000574   COMPRESSED   0x00000004   Data   RW         1424    .data               mc_w.l(errno.o)
    0x20000578   COMPRESSED   0x00000288   PAD
    0x20000800   COMPRESSED   0x0000026c   Data   RW          347    vtable              interrupt.o
    0x20000a6c   COMPRESSED   0x00000194   PAD
    0x20000c00        -       0x00000508   Zero   RW          739    .bss                uart_app.o
    0x20001108        -       0x00000068   Zero   RW          795    .bss                adc_app.o
    0x20001170        -       0x0000000c   Zero   RW          827    .bss                icm20608.o
    0x2000117c        -       0x00000024   Zero   RW          863    .bss                imu.o
    0x200011a0        -       0x00000020   Zero   RW          951    .bss                qei_app.o
    0x200011c0        -       0x00000078   Zero   RW         1024    .bss                pid_app.o
    0x20001238        -       0x00000200   Zero   RW          732    STACK               startup_rvmdk.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       964         24          0          3          0      15800   adc.o
       840         72          0          4        104       3160   adc_app.o
        38          0          0          0          0        660   cpu.o
         0          0       1275          0          0        539   glcdfont.o
      2040         30        528          0          0      14312   gpio.o
       904         32        128          0          0      12485   i2c.o
       636         32          0         16         12       2032   icm20608.o
      2912        128          0        100         36       6128   imu.o
       816         46        268        620          0       4828   interrupt.o
       292         60          0          4          0       1175   key_app.o
       580         48         10         40          0      13028   main.o
       912         10         16          1          0       5346   motor_app.o
      1180         44          0          0          0       5043   myiic.o
      3412         56       8552          0          0      10361   oled.o
       148         20          0          0          0       1546   oled_app.o
       572          6          0          0          0       3248   pid.o
       648         40          0         93        120       3815   pid_app.o
      1118         32          0          0          0      10353   pwm.o
      1132        112          0          0          0       3415   pwm_app.o
       756         98          0          0          0       3588   pwm_debug.o
       288         22          0          0          0       4111   qei.o
       688         62          0          0         32       2822   qei_app.o
        96         12          0         28          0       1518   scheduler.o
      4936        142          0       1063          0      20085   ssd1306.o
       648        622          0          0        512        284   startup_rvmdk.o
      2910        154        460          0          0      14553   sysctl.o
       156          0          0          0          0       1864   systick.o
       240         16          0          4          0       3129   systicktime.o
       760         20        160          0          0      10382   timer.o
       816         22        128          0          0      11072   uart.o
      1020         94          0         18       1288       4414   uart_app.o
       952         40         45          5          0       3677   uartstdio.o
       812         18          0          0          0      21457   udma.o

    ----------------------------------------------------------------------
     34228       <USER>      <GROUP>       2012       2508     220230   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         6          0          6         13        404          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       300         42          0          0          0        176   asinf.o
       684         90          0          0          0        208   atan2f.o
       336         56          0          0          0        136   cosf.o
        38          0          0          0          0        116   fpclassifyf.o
        44         12          0          0          0        464   funder.o
       340         24         32          0          0        160   rredf.o
       400         56          0          0          0        212   sinf.o
       120          0          0          0          0        272   sqrtf.o
        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2338        100          0          0          0        768   printfa.o
         0          0          0          4          0          0   stdout.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      6380        <USER>         <GROUP>        656          0       4180   Library Totals
        10          0          0        648          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2262        280         32          0          0       1744   m_wm.l
      2838        122          0          8          0       1456   mc_w.l
      1270          0          0          0          0        980   mf_w.l

    ----------------------------------------------------------------------
      6380        <USER>         <GROUP>        656          0       4180   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     40608       2516      11640       2668       2508     214646   Grand Totals
     40608       2516      11640        544       2508     214646   ELF Image Totals (compressed)
     40608       2516      11640        544          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                52248 (  51.02kB)
    Total RW  Size (RW Data + ZI Data)              5176 (   5.05kB)
    Total ROM Size (Code + RO Data + RW Data)      52792 (  51.55kB)

==============================================================================

