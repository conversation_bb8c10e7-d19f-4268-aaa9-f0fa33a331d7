#ifndef __PID_APP_H
#define __PID_APP_H

#include <stdint.h>
#include <stdbool.h>
#include "pid.h"
#include "motor_app.h"
#include "qei_app.h"

// 调试和上报相关定义
#define PID_REPORT_ENABLE 1    // 开启PID信息上报
#define PID_DEBUG_ENABLE 0     // 开启详细调试信息
#define PID_REPORT_PERIOD 20   // 上报周期 (ms)
#define PID_REPORT_SCALE 100   // 上报数据放大系数

// PID参数结构体
typedef struct
{
    float kp;          // 比例系数
    float ki;          // 积分系数
    float kd;          // 微分系数
    float sample_time; // 采样时间
    float out_min;     // 输出最小值
    float out_max;     // 输出最大值
    float i_min;       // 积分项最小值
    float i_max;       // 积分项最大值
    float deadzone;    // 死区大小
} PidParams_t;

// 函数声明
void pid_app_init(void);
void pid_app_set_target_speed(float left_rpm, float right_rpm);
void pid_app_update_speed(void);
void pid_app_start(void);
void pid_app_stop(void);
void pid_app_calc(void);
void pid_app_task(void);
void pid_app_set_left_params(PidParams_t params);
void pid_app_set_right_params(PidParams_t params);

#endif /* __PID_APP_H */ 

